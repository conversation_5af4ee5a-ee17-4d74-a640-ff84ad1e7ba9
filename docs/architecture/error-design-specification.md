# HarmoniaKV 错误设计规范

## 1. 设计概述

### 1.1 文档目的

本文档定义了 HarmoniaKV 分布式键值存储系统的完整错误处理设计，包括错误分层架构、错误代码规范、Details 字段结构和客户端处理策略。

### 1.2 设计原则

HarmoniaKV 采用**两层错误机制**，明确区分传输层错误和应用层错误的职责：

| 错误层次             | 职责范围                           | 返回方式                        | 典型场景                           |
| -------------------- | ---------------------------------- | ------------------------------- | ---------------------------------- |
| **传输层错误** | 消息传递过程中的基础设施问题       | gRPC `error` 返回值           | 网络连接失败、服务不可用、协议错误 |
| **应用层错误** | 业务逻辑执行过程中的分布式系统问题 | `InternalResponse.Error` 字段 | Quorum失败、版本冲突、数据不存在   |

### 1.3 设计目标

- **职责清晰**：不同层次的错误使用不同的处理机制
- **信息丰富**：应用层错误携带详细的诊断信息
- **处理精确**：支持基于错误类型的智能重试策略
- **可观测性**：便于监控、告警和故障排查
- **向前兼容**：支持错误字段的渐进式演进

## 2. 错误结构定义

### 2.1 Protobuf 错误结构

```protobuf
message Error {
  int32 code = 1;                      // ErrorCode 枚举值
  string message = 2;                  // 人类可读的错误消息
  map<string, string> details = 3;     // 结构化错误详情
  string category = 4;                 // 错误分类
  bool retryable = 5;                  // 是否可重试
  int32 retry_after_seconds = 6;       // 建议重试间隔（秒）
}
```

### 2.2 应用层错误代码

```protobuf
enum ErrorCode {
  // === 成功和通用错误 ===
  ERROR_CODE_SUCCESS = 0;
  ERROR_CODE_UNKNOWN = 1;
  ERROR_CODE_INVALID_REQUEST = 2;
  ERROR_CODE_TIMEOUT = 3;                    // 应用层超时（非网络超时）
  
  // === Quorum 和分布式协调错误 (2000-2999) ===
  ERROR_CODE_QUORUM_READ_FAILED = 2001;      // 读取Quorum失败
  ERROR_CODE_QUORUM_WRITE_FAILED = 2002;     // 写入Quorum失败  
  ERROR_CODE_INSUFFICIENT_REPLICAS = 2003;   // 可用副本不足
  ERROR_CODE_COORDINATOR_UNAVAILABLE = 2004; // 协调节点不可用
  ERROR_CODE_REPLICA_NOT_RESPONDING = 2005;  // 副本节点无响应（应用层检测）
  
  // === 版本冲突和一致性错误 (3000-3999) ===
  ERROR_CODE_VERSION_CONFLICT = 3001;        // 版本向量冲突
  ERROR_CODE_MERGE_FAILED = 3002;           // 版本合并失败
  ERROR_CODE_ANTI_ENTROPY_FAILED = 3003;    // 反熵同步失败
  ERROR_CODE_STALE_READ = 3004;             // 读取到过期数据
  ERROR_CODE_CONCURRENT_MODIFICATION = 3005; // 并发修改冲突
  
  // === 集群管理和成员错误 (4000-4999) ===
  ERROR_CODE_NODE_JOIN_FAILED = 4001;       // 节点加入失败
  ERROR_CODE_NODE_LEAVE_FAILED = 4002;      // 节点离开失败
  ERROR_CODE_GOSSIP_FAILED = 4003;          // Gossip协议失败
  ERROR_CODE_CLUSTER_STATE_INCONSISTENT = 4004; // 集群状态不一致
  ERROR_CODE_MEMBERSHIP_CHANGE_REJECTED = 4005;  // 成员变更被拒绝
  ERROR_CODE_SPLIT_BRAIN_DETECTED = 4006;   // 检测到脑裂
  
  // === 存储和数据错误 (5000-5999) ===
  ERROR_CODE_KEY_NOT_FOUND = 5001;          // 键不存在
  ERROR_CODE_STORAGE_FULL = 5002;           // 存储空间不足
  ERROR_CODE_STORAGE_CORRUPTION = 5003;     // 数据损坏
  ERROR_CODE_HINTED_HANDOFF_FAILED = 5004;  // 暗示移交失败
  ERROR_CODE_INVALID_KEY_FORMAT = 5005;     // 键格式无效
  ERROR_CODE_VALUE_TOO_LARGE = 5006;        // 值过大
  
  // === 认证和授权错误 (6000-6999) ===
  ERROR_CODE_UNAUTHORIZED = 6001;           // 未授权
  ERROR_CODE_TOKEN_EXPIRED = 6002;          // Token过期
  ERROR_CODE_PERMISSION_DENIED = 6003;      // 权限不足
  ERROR_CODE_INVALID_CREDENTIALS = 6004;    // 凭据无效
}
```

## 3. 两层错误机制详解

### 3.1 传输层错误（gRPC Error）

**职责**：处理消息传递过程中的基础设施问题

| 错误类型     | gRPC Status Code           | 说明                             | 处理策略               |
| ------------ | -------------------------- | -------------------------------- | ---------------------- |
| 网络连接失败 | `codes.Unavailable`      | TCP连接断开、DNS解析失败         | 重新连接、服务发现     |
| 连接超时     | `codes.DeadlineExceeded` | gRPC超时、网络延迟过高           | 增加超时时间、重试     |
| 服务不可用   | `codes.Unavailable`      | 目标服务进程死亡、端口未监听     | 故障转移、健康检查     |
| 协议错误     | `codes.Internal`         | Protobuf序列化失败、gRPC框架错误 | 检查消息格式、升级版本 |
| 认证失败     | `codes.Unauthenticated`  | TLS证书错误、认证token无效       | 重新认证、更新证书     |

**代码示例**：

```go
// 服务端 - 传输层错误
func (s *server) InternalCommand(ctx context.Context, req *InternalRequest) (*InternalResponse, error) {
    // 关键系统故障，阻止响应传递
    if criticalSystemFailure {
        return nil, status.Errorf(codes.Internal, "system critical failure")
        //     ^^^^ 客户端收不到 Response
    }
}

// 客户端处理
resp, err := client.InternalCommand(ctx, req)
if err != nil {
    grpcErr := status.Convert(err)
    switch grpcErr.Code() {
    case codes.Unavailable:
        return c.reconnectAndRetry(req)  // 重新连接
    case codes.DeadlineExceeded:
        return c.retryWithLongerTimeout(req)  // 增加超时
    }
}
```

### 3.2 应用层错误（Response.Error）

**职责**：处理业务逻辑执行过程中的分布式系统问题

| 错误类型       | ErrorCode                           | 说明                     | 处理策略             |
| -------------- | ----------------------------------- | ------------------------ | -------------------- |
| Quorum读取失败 | `ERROR_CODE_QUORUM_READ_FAILED`   | 可用副本不足，但网络正常 | 降级到最终一致性读取 |
| 版本冲突       | `ERROR_CODE_VERSION_CONFLICT`     | 检测到并发修改冲突       | 版本合并或重新读取   |
| 键不存在       | `ERROR_CODE_KEY_NOT_FOUND`        | 在所有副本中都找不到键   | 返回NotFound给客户端 |
| 存储满         | `ERROR_CODE_STORAGE_FULL`         | 本地存储空间不足         | 数据压缩或扩容       |
| 集群脑裂       | `ERROR_CODE_SPLIT_BRAIN_DETECTED` | 检测到集群分区           | 停止写入，等待修复   |

**代码示例**：

```go
// 服务端 - 应用层错误
func (s *server) InternalCommand(ctx context.Context, req *InternalRequest) (*InternalResponse, error) {
    // Quorum 读取失败，但网络通信正常
    result, quorumErr := s.performQuorumRead(req.Key)
    if quorumErr != nil {
        return &InternalResponse{
            RequestId: req.RequestId,
            Success:   false,
            Error: &Error{
                Code:     ERROR_CODE_QUORUM_READ_FAILED,
                Message:  "insufficient replicas responded",
                Category: "quorum",
                Retryable: true,
                Details: map[string]string{
                    "operation_id":   "read-op-12345",
                    "success_count":  "1",
                    "required_count": "2",
                    "total_replicas": "3",
                },
            },
        }, nil  // 注意：gRPC error 是 nil，客户端能收到完整响应
    }
  
    return &InternalResponse{Success: true, Data: result}, nil
}
```

## 4. Details 字段规范

### 4.1 Quorum 相关错误

#### ERROR_CODE_QUORUM_READ_FAILED (2001)

**场景**：读取 Quorum 失败
**Details 字段**：

```yaml
operation_id: "read-op-12345"         # 操作ID
key: "user:123"                      # 读取的键
coordinator: "node-1"                # 协调节点
target_nodes: "node-1,node-2,node-3" # 目标节点
success_nodes: "node-1"              # 成功节点
failed_nodes: "node-2,node-3"        # 失败节点
success_count: "1"                   # 成功数量
required_count: "2"                  # 需要数量
total_nodes: "3"                     # 总节点数
read_level: "QUORUM"                 # 读取级别
timeout_ms: "5000"                   # 超时时间
```

**客户端处理**：

```go
if resp.Error.Code == ERROR_CODE_QUORUM_READ_FAILED {
    successCount, _ := strconv.Atoi(resp.Error.Details["success_count"])
    requiredCount, _ := strconv.Atoi(resp.Error.Details["required_count"])
  
    if successCount > 0 {
        // 部分成功，降级到最终一致性读取
        return client.ReadEventual(key)
    } else {
        // 完全失败，需要重试或报错
        return nil, fmt.Errorf("all replicas unavailable for key %s", key)
    }
}
```

#### ERROR_CODE_QUORUM_WRITE_FAILED (2002)

**场景**：写入 Quorum 失败
**Details 字段**：

```yaml
operation_id: "write-op-67890"
key: "product:456"
coordinator: "node-2"
target_nodes: "node-1,node-2,node-3"
success_nodes: "node-2"
failed_nodes: "node-1,node-3"
success_count: "1"
required_count: "2"
write_level: "QUORUM"
hinted_handoff: "enabled"            # 是否启用 hinted handoff
hints_stored: "2"                    # 存储的 hint 数量
```

### 4.2 版本冲突和一致性错误

#### ERROR_CODE_VERSION_CONFLICT (3001)

**场景**：版本冲突检测到
**Details 字段**：

```yaml
key: "shared:data"
conflict_type: "version_vector"       # 冲突类型
conflicting_nodes: "node-1,node-3"   # 冲突节点
version_vectors: |                   # 版本向量（JSON格式）
  [
    {"node-1":5,"node-2":3,"node-3":2},
    {"node-1":4,"node-2":4,"node-3":3}
  ]
merge_strategy: "timestamp"          # 合并策略
auto_resolve: "true"                 # 是否自动解决
conflict_resolver: "last_write_wins" # 冲突解决器
```

**客户端处理**：

```go
if resp.Error.Code == ERROR_CODE_VERSION_CONFLICT {
    autoResolve := resp.Error.Details["auto_resolve"] == "true"
    if autoResolve {
        // 等待自动解决
        time.Sleep(time.Second)
        return client.Retry(request)
    } else {
        // 需要人工干预或应用层解决
        return handleVersionConflict(resp.Error.Details)
    }
}
```

### 4.3 集群管理错误

#### ERROR_CODE_GOSSIP_FAILED (4003)

**场景**：Gossip 协议失败
**Details 字段**：

```yaml
gossip_round: "127"
source_node: "node-1"
target_node: "node-2"
message_type: "membership_update"
message_size: "1024"
failure_reason: "encoding_error"
retry_count: "3"
next_retry: "30s"
```

#### ERROR_CODE_SPLIT_BRAIN_DETECTED (4006)

**场景**：检测到集群脑裂
**Details 字段**：

```yaml
detection_node: "node-1"
partition_nodes: "node-2,node-3"     # 分区节点列表
majority_nodes: "node-1,node-4,node-5" # 多数派节点列表
partition_start: "2024-01-15T10:45:00Z" # 分区开始时间
last_gossip: "2024-01-15T10:40:00Z"  # 最后 gossip 时间
resolution_strategy: "stop_minority" # 解决策略
```

### 4.4 存储相关错误

#### ERROR_CODE_KEY_NOT_FOUND (5001)

**场景**：键不存在
**Details 字段**：

```yaml
key: "missing:key"
searched_nodes: "node-1,node-2,node-3"
read_level: "QUORUM"
last_modified: ""                    # 空表示从未存在
tombstone: "false"                   # 是否是墓碑记录
```

#### ERROR_CODE_HINTED_HANDOFF_FAILED (5004)

**场景**：Hinted Handoff 失败
**Details 字段**：

```yaml
original_node: "node-2"              # 原始目标节点
hint_node: "node-1"                  # 存储 hint 的节点
operation_id: "write-op-98765"
key: "delayed:write"
hint_count: "15"                     # 当前 hint 数量
hint_age: "300s"                     # hint 存在时间
delivery_attempts: "5"               # 投递尝试次数
last_attempt: "2024-01-15T11:00:00Z"
node_status: "still_down"            # 原始节点状态
max_hints_reached: "false"           # 是否达到 hint 上限
```

## 5. 客户端错误处理最佳实践

### 5.1 两层错误处理模式

```go
func (c *Client) robustCall(req *Request) (*Response, error) {
    for attempt := 0; attempt < maxRetries; attempt++ {
        resp, transportErr := c.grpcClient.Call(req)
      
        // 第一层：传输层错误处理
        if transportErr != nil {
            if shouldRetryTransportError(transportErr) {
                time.Sleep(backoffDelay(attempt))
                continue  // 重试
            }
            return nil, transportErr  // 不可重试的传输错误
        }
      
        // 第二层：应用层错误处理  
        if !resp.Success && resp.Error != nil {
            if shouldRetryBusinessError(resp.Error) {
                time.Sleep(businessBackoffDelay(resp.Error))
                continue  // 重试
            }
            return nil, wrapBusinessError(resp.Error)  // 不可重试的业务错误
        }
      
        return resp, nil  // 成功
    }
}
```

### 5.2 智能错误处理器

```go
type ErrorHandler struct {
    maxRetries         int
    baseDelay          time.Duration
    retryableCategories map[string]bool
}

func (h *ErrorHandler) AnalyzeError(err *Error, attemptCount int) *RetryDecision {
    // 检查是否可重试
    if !err.Retryable || attemptCount >= h.maxRetries {
        return &RetryDecision{ShouldRetry: false}
    }
  
    // 使用服务端建议的重试间隔
    if err.RetryAfterSeconds > 0 {
        return &RetryDecision{
            ShouldRetry: true,
            Delay:       time.Duration(err.RetryAfterSeconds) * time.Second,
            Reason:      "server-suggested retry delay",
        }
    }
  
    // 基于错误类型的智能处理
    switch err.Code {
    case ERROR_CODE_QUORUM_READ_FAILED:
        return h.handleQuorumReadError(err)
    case ERROR_CODE_VERSION_CONFLICT:
        return h.handleVersionConflictError(err)
    default:
        return h.genericRetryDecision(attemptCount)
    }
}

func (h *ErrorHandler) handleQuorumReadError(err *Error) *RetryDecision {
    successCount, _ := strconv.Atoi(err.Details["success_count"])
    if successCount > 0 {
        return &RetryDecision{
            ShouldRetry: true,
            Delay:       50 * time.Millisecond,  // 快速重试
            Reason:      "partial success, quick retry",
            Alternative: "consider degrading to eventual consistency",
        }
    }
  
    return &RetryDecision{
        ShouldRetry: true,
        Delay:       1 * time.Second,  // 较长延迟
        Reason:      "complete failure, backoff retry",
    }
}
```

### 5.3 错误监控和统计

```go
type ErrorMetrics struct {
    transportErrors *prometheus.CounterVec
    businessErrors  *prometheus.CounterVec
    retryCounter    *prometheus.CounterVec
}

func (m *ErrorMetrics) RecordError(transportErr error, businessErr *Error) {
    if transportErr != nil {
        m.transportErrors.WithLabelValues(
            grpcErrorCode(transportErr),
        ).Inc()
    }
  
    if businessErr != nil {
        m.businessErrors.WithLabelValues(
            businessErr.Category,
            strconv.Itoa(int(businessErr.Code)),
            businessErr.Details["operation_type"],
        ).Inc()
      
        if businessErr.Retryable {
            m.retryCounter.WithLabelValues(
                businessErr.Category,
                "scheduled",
            ).Inc()
        }
    }
}
```

## 6. 服务端错误构建最佳实践

### 6.1 错误构建器模式

```go
type ErrorBuilder struct {
    code     ErrorCode
    message  string
    details  map[string]string
    category string
}

func NewQuorumReadError(operation *QuorumOperation) *Error {
    return &Error{
        Code:     ERROR_CODE_QUORUM_READ_FAILED,
        Message:  "quorum read failed",
        Category: "quorum",
        Retryable: true,
        RetryAfterSeconds: 1,
        Details: map[string]string{
            "operation_id":   operation.ID,
            "key":           string(operation.Key),
            "coordinator":   operation.Coordinator,
            "success_count": strconv.Itoa(operation.SuccessCount),
            "required_count": strconv.Itoa(operation.RequiredCount),
            "target_nodes":  strings.Join(operation.TargetNodes, ","),
            "success_nodes": strings.Join(operation.SuccessNodes, ","),
            "failed_nodes":  strings.Join(operation.FailedNodes, ","),
        },
    }
}

func NewVersionConflictError(key []byte, conflicts []VersionVector) *Error {
    conflictData, _ := json.Marshal(conflicts)
    return &Error{
        Code:     ERROR_CODE_VERSION_CONFLICT,
        Message:  "version conflict detected",
        Category: "consistency",
        Retryable: true,
        Details: map[string]string{
            "key":              string(key),
            "conflict_type":    "version_vector",
            "version_vectors":  string(conflictData),
            "auto_resolve":     "true",
            "merge_strategy":   "timestamp",
        },
    }
}
```

### 6.2 服务端错误处理流程

```go
func (s *server) InternalCommand(ctx context.Context, req *InternalRequest) (*InternalResponse, error) {
    // 1. 优先检查系统级问题（返回传输层错误）
    if s.isSystemCritical() {
        return nil, status.Errorf(codes.Internal, "system in critical state")
    }
  
    // 2. 执行业务逻辑，构建应用层错误
    result, businessErr := s.executeBusinessLogic(req)
    if businessErr != nil {
        return &InternalResponse{
            RequestId: req.RequestId,
            Success:   false,
            Error:     businessErr,  // 详细的业务错误信息
        }, nil  // gRPC error = nil
    }
  
    // 3. 成功响应
    return &InternalResponse{
        RequestId: req.RequestId,
        Success:   true,
        Data:      result,
    }, nil
}
```

## 7. 实际应用场景

### 7.1 场景1：网络分区处理

```go
// 情况1：gRPC连接完全断开（传输层错误）
resp, err := client.InternalCommand(ctx, req)
if err != nil {
    if status.Code(err) == codes.Unavailable {
        // 处理：重新连接、故障转移到其他节点
        return client.failoverAndRetry(req)
    }
}

// 情况2：连接正常，但应用层检测到集群分区（应用层错误）
if resp.Error != nil && resp.Error.Code == ERROR_CODE_SPLIT_BRAIN_DETECTED {
    // 处理：根据分区策略决定是否停止写入
    partitionStrategy := resp.Error.Details["resolution_strategy"]
    if partitionStrategy == "stop_minority" {
        return errors.New("cluster partitioned, writes stopped")
    }
}
```

### 7.2 场景2：Quorum 操作优化

```go
func (c *Client) intelligentRead(key string) ([]byte, error) {
    resp, err := c.quorumRead(key)
  
    // 传输层错误：基础设施问题
    if err != nil {
        return nil, err
    }
  
    // 应用层错误：业务逻辑问题
    if !resp.Success && resp.Error != nil {
        switch resp.Error.Code {
        case ERROR_CODE_QUORUM_READ_FAILED:
            successCount := resp.Error.Details["success_count"]
            if successCount != "0" {
                // 部分成功，降级到最终一致性读取
                log.Infof("Degrading to eventual consistency read for key %s", key)
                return c.eventualRead(key)
            }
            // 完全失败，返回错误
            return nil, fmt.Errorf("all replicas unavailable")
          
        case ERROR_CODE_KEY_NOT_FOUND:
            return nil, ErrKeyNotFound
          
        default:
            return nil, fmt.Errorf("read failed: %s", resp.Error.Message)
        }
    }
  
    return resp.Data, nil
}
```

## 8. 版本兼容性和演进策略

### 8.1 Details 字段演进策略

- **添加字段**：向后兼容，客户端忽略未知字段
- **修改字段语义**：需要版本升级，保持向后兼容期
- **删除字段**：需要主版本升级，提供迁移期

### 8.2 客户端兼容性处理

```go
func safeGetDetail(details map[string]string, key string, defaultValue string) string {
    if value, exists := details[key]; exists {
        return value
    }
    return defaultValue
}

// 安全的字段访问模式
func handleQuorumError(err *Error) {
    // 检查字段是否存在再使用
    if operationID := safeGetDetail(err.Details, "operation_id", ""); operationID != "" {
        log.WithField("operation_id", operationID).Error("Quorum operation failed")
    }
  
    // 使用默认值处理可能缺失的字段
    successCount, _ := strconv.Atoi(safeGetDetail(err.Details, "success_count", "0"))
    requiredCount, _ := strconv.Atoi(safeGetDetail(err.Details, "required_count", "1"))
  
    if successCount >= requiredCount/2 {
        // 基于可用信息做决策
    }
}
```

### 8.3 错误代码演进

```go
// 新增错误代码时的向后兼容处理
func handleError(err *Error) error {
    switch err.Code {
    case ERROR_CODE_QUORUM_READ_FAILED:
        return handleQuorumReadError(err)
    case ERROR_CODE_VERSION_CONFLICT:
        return handleVersionConflictError(err)
    // ... 其他已知错误
    default:
        // 处理未知错误代码（可能是新版本引入的）
        if err.Category != "" {
            return handleByCategoryFallback(err)
        }
        return handleGenericError(err)
    }
}
```

## 9. 总结

### 9.1 核心优势

1. **错误职责清晰**：传输层 vs 应用层错误分工明确
2. **处理策略精确**：不同错误类型使用不同的恢复策略
3. **诊断信息丰富**：应用层错误携带详细的上下文信息
4. **监控告警精准**：分别统计基础设施和业务问题
5. **智能重试支持**：基于错误详情的自适应重试策略
6. **向前兼容性**：支持错误字段的渐进式演进

### 9.2 实施建议

1. **渐进式迁移**：优先实现核心错误类型，逐步完善
2. **统一错误构建**：使用错误构建器确保字段一致性
3. **监控先行**：尽早建立错误监控和告警机制
4. **文档同步**：错误字段变更时及时更新文档
5. **测试覆盖**：为各种错误场景编写充分的测试用例

### 9.3 与业界对比

这种两层错误设计遵循了现代分布式系统的最佳实践，与 Cassandra、etcd、Consul 等成熟系统的错误处理模式保持一致，确保了设计的可靠性和可维护性。
