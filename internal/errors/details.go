// Package errors provides strongly-typed error details for HarmoniaKV
package errors

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"
)

// ErrorDetails is the interface for all error detail types
type ErrorDetails interface {
	ToMap() map[string]string
	FromMap(map[string]string) error
	ErrorCode() int32
	Category() string
}

// NetworkUnreachableDetails for ERROR_CODE_NETWORK_UNREACHABLE (1001)
type NetworkUnreachableDetails struct {
	TargetNode      string    `json:"target_node"`
	TargetAddress   string    `json:"target_address"`
	LastSuccessful  time.Time `json:"last_successful"`
	FailureDuration string    `json:"failure_duration"`
	NetworkPath     string    `json:"network_path"`
}

func (d *NetworkUnreachableDetails) ErrorCode() int32 { return 1001 }
func (d *NetworkUnreachableDetails) Category() string { return "network" }

func (d *NetworkUnreachableDetails) ToMap() map[string]string {
	return map[string]string{
		"target_node":      d.<PERSON>,
		"target_address":   d.<PERSON>,
		"last_successful":  d.LastSuccessful.Format(time.RFC3339),
		"failure_duration": d.FailureDuration,
		"network_path":     d.NetworkPath,
	}
}

func (d *NetworkUnreachableDetails) FromMap(m map[string]string) error {
	d.TargetNode = m["target_node"]
	d.TargetAddress = m["target_address"]
	d.FailureDuration = m["failure_duration"]
	d.NetworkPath = m["network_path"]

	if lastSuccessful := m["last_successful"]; lastSuccessful != "" {
		if t, err := time.Parse(time.RFC3339, lastSuccessful); err == nil {
			d.LastSuccessful = t
		}
	}
	return nil
}

// QuorumReadFailedDetails for ERROR_CODE_QUORUM_READ_FAILED (2001)
type QuorumReadFailedDetails struct {
	OperationID   string   `json:"operation_id"`
	Key           string   `json:"key"`
	Coordinator   string   `json:"coordinator"`
	TargetNodes   []string `json:"target_nodes"`
	SuccessNodes  []string `json:"success_nodes"`
	FailedNodes   []string `json:"failed_nodes"`
	SuccessCount  int      `json:"success_count"`
	RequiredCount int      `json:"required_count"`
	TotalNodes    int      `json:"total_nodes"`
	ReadLevel     string   `json:"read_level"`
	TimeoutMs     int      `json:"timeout_ms"`
}

func (d *QuorumReadFailedDetails) ErrorCode() int32 { return 2001 }
func (d *QuorumReadFailedDetails) Category() string { return "quorum" }

func (d *QuorumReadFailedDetails) ToMap() map[string]string {
	targetNodes, _ := json.Marshal(d.TargetNodes)
	successNodes, _ := json.Marshal(d.SuccessNodes)
	failedNodes, _ := json.Marshal(d.FailedNodes)

	return map[string]string{
		"operation_id":   d.OperationID,
		"key":            d.Key,
		"coordinator":    d.Coordinator,
		"target_nodes":   string(targetNodes),
		"success_nodes":  string(successNodes),
		"failed_nodes":   string(failedNodes),
		"success_count":  strconv.Itoa(d.SuccessCount),
		"required_count": strconv.Itoa(d.RequiredCount),
		"total_nodes":    strconv.Itoa(d.TotalNodes),
		"read_level":     d.ReadLevel,
		"timeout_ms":     strconv.Itoa(d.TimeoutMs),
	}
}

func (d *QuorumReadFailedDetails) FromMap(m map[string]string) error {
	d.OperationID = m["operation_id"]
	d.Key = m["key"]
	d.Coordinator = m["coordinator"]
	d.ReadLevel = m["read_level"]

	// Parse JSON arrays
	if targetNodes := m["target_nodes"]; targetNodes != "" {
		json.Unmarshal([]byte(targetNodes), &d.TargetNodes)
	}
	if successNodes := m["success_nodes"]; successNodes != "" {
		json.Unmarshal([]byte(successNodes), &d.SuccessNodes)
	}
	if failedNodes := m["failed_nodes"]; failedNodes != "" {
		json.Unmarshal([]byte(failedNodes), &d.FailedNodes)
	}

	// Parse integers
	if successCount := m["success_count"]; successCount != "" {
		if count, err := strconv.Atoi(successCount); err == nil {
			d.SuccessCount = count
		}
	}
	if requiredCount := m["required_count"]; requiredCount != "" {
		if count, err := strconv.Atoi(requiredCount); err == nil {
			d.RequiredCount = count
		}
	}
	if totalNodes := m["total_nodes"]; totalNodes != "" {
		if count, err := strconv.Atoi(totalNodes); err == nil {
			d.TotalNodes = count
		}
	}
	if timeoutMs := m["timeout_ms"]; timeoutMs != "" {
		if timeout, err := strconv.Atoi(timeoutMs); err == nil {
			d.TimeoutMs = timeout
		}
	}

	return nil
}

// VersionConflictDetails for ERROR_CODE_VERSION_CONFLICT (3001)
type VersionConflictDetails struct {
	Key              string              `json:"key"`
	ConflictType     string              `json:"conflict_type"`
	ConflictingNodes []string            `json:"conflicting_nodes"`
	VersionVectors   []map[string]uint64 `json:"version_vectors"`
	MergeStrategy    string              `json:"merge_strategy"`
	AutoResolve      bool                `json:"auto_resolve"`
	ConflictResolver string              `json:"conflict_resolver"`
}

func (d *VersionConflictDetails) ErrorCode() int32 { return 3001 }
func (d *VersionConflictDetails) Category() string { return "consistency" }

func (d *VersionConflictDetails) ToMap() map[string]string {
	conflictingNodes, _ := json.Marshal(d.ConflictingNodes)
	versionVectors, _ := json.Marshal(d.VersionVectors)

	return map[string]string{
		"key":               d.Key,
		"conflict_type":     d.ConflictType,
		"conflicting_nodes": string(conflictingNodes),
		"version_vectors":   string(versionVectors),
		"merge_strategy":    d.MergeStrategy,
		"auto_resolve":      strconv.FormatBool(d.AutoResolve),
		"conflict_resolver": d.ConflictResolver,
	}
}

func (d *VersionConflictDetails) FromMap(m map[string]string) error {
	d.Key = m["key"]
	d.ConflictType = m["conflict_type"]
	d.MergeStrategy = m["merge_strategy"]
	d.ConflictResolver = m["conflict_resolver"]

	if autoResolve := m["auto_resolve"]; autoResolve != "" {
		if resolve, err := strconv.ParseBool(autoResolve); err == nil {
			d.AutoResolve = resolve
		}
	}

	if conflictingNodes := m["conflicting_nodes"]; conflictingNodes != "" {
		json.Unmarshal([]byte(conflictingNodes), &d.ConflictingNodes)
	}

	if versionVectors := m["version_vectors"]; versionVectors != "" {
		json.Unmarshal([]byte(versionVectors), &d.VersionVectors)
	}

	return nil
}

// ErrorDetailsFactory creates the appropriate ErrorDetails based on error code
type ErrorDetailsFactory struct{}

func (f *ErrorDetailsFactory) CreateDetails(errorCode int32) ErrorDetails {
	switch errorCode {
	case 1001:
		return &NetworkUnreachableDetails{}
	case 2001:
		return &QuorumReadFailedDetails{}
	case 3001:
		return &VersionConflictDetails{}
	// Add more cases as needed
	default:
		return nil
	}
}

// ParseErrorDetails parses a map[string]string into strongly-typed details
func ParseErrorDetails(errorCode int32, details map[string]string) (ErrorDetails, error) {
	factory := &ErrorDetailsFactory{}
	errorDetails := factory.CreateDetails(errorCode)
	if errorDetails == nil {
		return nil, fmt.Errorf("unknown error code: %d", errorCode)
	}

	if err := errorDetails.FromMap(details); err != nil {
		return nil, fmt.Errorf("failed to parse error details: %w", err)
	}

	return errorDetails, nil
}
