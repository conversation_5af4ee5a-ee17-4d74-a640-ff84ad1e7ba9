package config

import (
	"sync"

	"github.com/google/uuid"     // 新增 UUID 导入
	"github.com/sirupsen/logrus" // 假设已有日志库
	"github.com/spf13/viper"     // 新增 Viper 导入
)

var (
	defaultConfig *config
	onece         sync.Once
	mutex         sync.Mutex
)

// 扩展 config 结构体，添加新字段（如 Peers, ClusterName）
type config struct {
	ClusterName string   // 集群名称
	Peers       []string // 种子节点列表
	NodeID      string   // 节点 ID
	NodeAddr    string   // 节点地址

	Replicas     uint32 // 副本个数
	ReadConcern  uint32 // 读 Quorum
	WriteConcern uint32 // 写 Quorum
	VnodeCount   int    // 虚拟节点个数
	RandomNode   int    // Gossip 随机发送节点个数
}

// Init 初始化全局配置，参考 Cassandra 的 YAML + env 加载
// 新增: 如果 node_id 未配置，自动生成 UUID
func Init(configPath string) {
	mutex.Lock()
	defer mutex.Unlock()
	onece.Do(func() {
		vp := viper.New()
		vp.SetConfigName("harmoniakv") // 文件名（无扩展）
		vp.SetConfigType("yaml")
		vp.AddConfigPath(".")                // 当前目录
		vp.AddConfigPath("config/")          // config/ 子目录
		vp.AddConfigPath("/etc/harmoniakv/") // 系统路径（可选）

		// 如果指定了 configPath，覆盖默认路径
		if configPath != "" {
			vp.SetConfigFile(configPath)
		}

		// 读取 YAML（忽略错误，使用默认）
		if err := vp.ReadInConfig(); err != nil {
			logrus.Warnf("Failed to read config file: %v, using defaults", err)
		}

		// 自动绑定环境变量（前缀 HARMONIAKV_，例如 HARMONIAKV_REPLICAS）
		vp.AutomaticEnv()
		vp.SetEnvPrefix("HARMONIAKV")

		// 设置默认值
		vp.SetDefault("cluster_name", "DefaultCluster")
		vp.SetDefault("peers", []string{})
		vp.SetDefault("node_id", "") // 空字符串表示未设，将生成 UUID
		vp.SetDefault("node_addr", "127.0.0.1:8080")
		vp.SetDefault("vnode_count", 10)
		vp.SetDefault("random_node", 3)
		vp.SetDefault("replicas", 1)
		vp.SetDefault("read_concern", 1)
		vp.SetDefault("write_concern", 1)

		// 绑定到全局 config
		nodeID := vp.GetString("node_id")
		if nodeID == "" {
			nodeID = uuid.New().String() // 自动生成 UUID，作为节点唯一标识
			logrus.Infof("Generated node_id: %s", nodeID)
		}

		nodeAddr := vp.GetString("node_addr")
		if nodeAddr == "" {
			logrus.Warn("node_addr not set, using default: 127.0.0.1:8080")
		}

		defaultConfig = &config{
			ClusterName:  vp.GetString("cluster_name"),
			Peers:        vp.GetStringSlice("peers"),
			NodeID:       nodeID,   // 使用生成或配置的值
			NodeAddr:     nodeAddr, // 使用配置或默认
			VnodeCount:   vp.GetInt("vnode_count"),
			RandomNode:   vp.GetInt("random_node"),
			Replicas:     uint32(vp.GetInt("replicas")),
			ReadConcern:  uint32(vp.GetInt("read_concern")),
			WriteConcern: uint32(vp.GetInt("write_concern")),
		}
	})
}

func VnodeCount() int {
	return defaultConfig.VnodeCount
}

func RandomNode() int {
	return defaultConfig.RandomNode
}

// 新增 getter 函数（示例）
func ClusterName() string {
	return defaultConfig.ClusterName
}

func Peers() []string {
	return defaultConfig.Peers
}

func NodeID() string {
	return defaultConfig.NodeID
}

func NodeAddr() string {
	return defaultConfig.NodeAddr
}

func ReadConcern() int {
	return int(defaultConfig.ReadConcern)
}

func WriteConcern() int {
	return int(defaultConfig.WriteConcern)
}

func Replicas() int {
	return int(defaultConfig.Replicas)
}

func LocalId() string {
	// TODO
	return ""
}

func InternalToken() string {
	return "secret-internal-token" // 示例，实际应安全生成
}
