---
toc:
  depth_from: 1
  depth_to: 6
  ordered: false
---
## 1. 文档概述

### 1.1 文档目的

本文旨在描述**HarmoniaKV 分布式键值存储系统**的架构设计方案，为开发和运维人员提供详细的技术指导。

### 1.2 修订记录

| 版本号 | 日期       | 修改内容                       | 修改人       | 审核人 |
| ------ | ---------- | ------------------------------ | ------------ | ------ |
| v1.0   | 2024年10月 | 初稿                           | AI Assistant | -      |
| v1.1   | 2024年12月 | 完善架构图、详细设计和部署架构 | AI Assistant | -      |

### 1.3 词汇表

| 词汇           | 含义                         |
| -------------- | ---------------------------- |
| Quorum         | 读写一致性模型，N/R/W 配置   |
| Gossip         | 集群成员管理和状态传播协议   |
| Handoff        | 暗示移交，用于故障节点恢复   |
| Version Vector | 版本向量，用于冲突解决       |
| WAL            | Write-Ahead Log，预写日志    |
| gRPC           | Google Remote Procedure Call |

## 2. 系统背景与目标

### 2.1 业务背景

HarmoniaKV 是一个类似于 Amazon Dynamo 的分布式键值存储系统，用于处理大规模数据存储需求，支持高并发读写、动态节点扩展和故障恢复。适用于云服务、缓存系统等场景，解决传统数据库的单点故障和扩展瓶颈。

### 2.2 设计目标

#### 2.2.1 功能需求

- 支持键值对的 Get/Put 操作
- 动态添加/移除节点
- 通过 Gossip 协议实现集群发现和状态同步
- 使用版本向量确保最终一致性
- 支持 Hinted Handoff 和反熵修复

#### 2.2.2 非功能需求

- 可扩展性：支持动态节点扩展，无单点瓶颈
- 性能：读写延迟 < 10ms，TPS > 10,000；节点覆盖率 >70%
- 可靠性：99.99% 可用性，支持 Quorum 容错
- 安全性：内部通信添加 token 认证

### 2.3 系统约束

- 使用 Go 语言开发
- 依赖 gRPC for 通信
- 内存存储为主，未来集成持久化

## 3. 系统架构设计

### 3.1 系统上下文图

```mermaid
C4Context
    title System Context Diagram for HarmoniaKV

    Person(client, "Client Application", "应用程序客户端")
    System(harmoniakv, "HarmoniaKV Cluster", "分布式键值存储集群")
    System_Ext(monitoring, "Monitoring System", "Prometheus/Grafana监控")
    System_Ext(logging, "Logging System", "日志收集系统")

    Rel(client, harmoniakv, "读写请求", "gRPC:50051")
    Rel(harmoniakv, monitoring, "指标数据", "HTTP")
    Rel(harmoniakv, logging, "日志数据", "TCP")
```

### 3.2 总体架构图

```mermaid
C4Container
    title Container Diagram for HarmoniaKV

    Person(client, "Client", "客户端应用")
  
    Container_Boundary(cluster, "HarmoniaKV Cluster") {
        Container(node1, "Node 1", "Go, gRPC", "协调器+存储节点")
        Container(node2, "Node 2", "Go, gRPC", "协调器+存储节点")
        Container(node3, "Node 3", "Go, gRPC", "协调器+存储节点")
    }

    Rel(client, node1, "Get/Put", "gRPC:50051")
    Rel(client, node2, "Get/Put", "gRPC:50051")
    Rel(client, node3, "Get/Put", "gRPC:50051")
  
    Rel(node1, node2, "复制/Gossip", "gRPC:50052")
    Rel(node2, node3, "复制/Gossip", "gRPC:50052")
    Rel(node3, node1, "复制/Gossip", "gRPC:50052")
```

### 3.3 数据流架构图

```mermaid
flowchart TD
    A[客户端请求] --> B[gRPC Server :50051]
    B --> C[Coordinator]
    C --> D[Cluster Manager]
    D --> E[一致性哈希环]
    E --> F[获取副本节点列表]
    F --> G{是否本地节点?}
    G -->|是| H[本地存储处理]
    G -->|否| I[Transport层发送]
    H --> J[版本向量更新]
    I --> K[等待副本响应]
    K --> L[Quorum检查]
    L --> M[返回结果]
    J --> M
```

### 3.4 架构决策

- 选择 gRPC over HTTP for 高效 RPC
- internal RPC 分离端口 + token 认证 for 安全
- 一致性哈希 for 数据分布
- 无主架构避免单点故障

## 4. 详细设计

### 4.1 模块设计

```mermaid
classDiagram
    class Cluster {
        +AddNode(node Node)
        +RemoveNode(nodeID string)
        +GetReplicas(key []byte, n int) []Node
        +HandleGossipMessage(msg GossipMessage)
        +GetNodes() []Node
    }
  
    class Node {
        +ID string
        +Address string
        +Store Storage
        +Cluster Cluster
        +CoordinateGet(key []byte) GetResponse
        +CoordinatePut(key []byte, value []byte) PutResponse
        +HandleCommand(cmd Command) Response
    }
  
    class Transport {
        +Send(address string, request Request) Response
        +AsyncSend(address string, request Request)
        +WaitResponses(count int) []Response
    }
  
    class Server {
        +Get(request GetRequest) GetResponse
        +Put(request PutRequest) PutResponse
        +InternalCommand(request InternalRequest) InternalResponse
    }
  
    class Storage {
        +Put(key []byte, value Value)
        +Get(key []byte) []Value
        +Delete(key []byte)
    }
  
    class Value {
        +Value []byte
        +VersionVector map[string]uint64
        +Timestamp int64
    }
  
    Node --> Cluster
    Node --> Storage
    Server --> Node
    Node --> Transport
    Storage --> Value
```

### 4.2 数据设计

#### 4.2.1 核心数据结构

```go
// 键值对数据结构
type Value struct {
    Value         []byte                 `json:"value"`
    VersionVector map[string]uint64      `json:"version_vector"`
    Timestamp     int64                  `json:"timestamp"`
}

// 节点信息
type Node struct {
    ID       string `json:"id"`
    Address  string `json:"address"`
    Status   string `json:"status"` // active, down, joining
    LastSeen int64  `json:"last_seen"`
}

// Gossip消息
type GossipMessage struct {
    Type      string            `json:"type"`
    NodeState map[string]*Node  `json:"node_state"`
    Timestamp int64             `json:"timestamp"`
}
```

#### 4.2.2 存储接口设计

```go
type Storage interface {
    Put(key []byte, value Value) error
    Get(key []byte) ([]Value, error)
    Delete(key []byte) error
    GetAll() map[string][]Value
    Close() error
}
```

### 4.3 接口设计

#### 4.3.1 gRPC 服务接口

参考 `pkg/api/v1/harmoniakv.proto`：

```protobuf
service Harmoniakv {
  rpc Get(GetRequest) returns (GetResponse);
  rpc Put(PutRequest) returns (PutResponse);
  rpc InternalCommand(InternalRequest) returns (InternalResponse);
}
```

#### 4.3.2 内部通信协议

```go
type InternalRequest struct {
    Token       string      `json:"token"`
    MessageId   string      `json:"message_id"`
    MessageType MessageType `json:"message_type"`
    From        string      `json:"from"`
    To          string      `json:"to"`
    Data        interface{} `json:"data"`
}
```

HarmoniaKV 定义了9种内部消息类型：

| 消息类型 | 用途 | 触发场景 |
|---------|------|---------|
| MessageTypeGossip | 集群状态同步 | 定期心跳、状态传播 |
| MessageTypeKvCommand | 数据读写操作 | 客户端请求、副本同步 |
| MessageTypeNodeJoin | 节点加入 | 集群扩容、故障恢复 |
| MessageTypeNodeLeave | 节点离开 | 计划下线、故障移除 |
| MessageTypeAntiEntropy | 反熵修复 | 数据一致性检查 |
| MessageTypeHintedHandoff | 暗示移交 | 副本不可用处理 |
| MessageTypeQuorumAck | 仲裁确认 | 读写操作确认 |
| MessageTypeVersionMerge | 版本合并 | 冲突版本解决 |
| MessageTypeClusterQuery | 集群查询 | 监控、运维查询 |

**详细的消息流程和处理逻辑请参考：[消息流程详细设计](./message-flows.md)**

#### 4.3.3 响应格式

```go
type InternalResponse struct {
    MessageId    string      `json:"message_id"`
    RequestType  MessageType `json:"request_type"`
    Success      bool        `json:"success"`
    Error        *Error      `json:"error,omitempty"`
    Data         interface{} `json:"data,omitempty"`
}
```

### 4.4 关键逻辑设计

#### 4.4.1 读操作流程

```mermaid
sequenceDiagram
    participant C as Client
    participant N1 as Node1(Coordinator)
    participant N2 as Node2
    participant N3 as Node3
  
    C->>N1: Get(key)
    N1->>N1: GetReplicas(key, N=3)
  
    par 并行读取副本
        N1->>N1: Local Get
        N1->>N2: Internal Get
        N1->>N3: Internal Get
    end
  
    N2-->>N1: Value + VersionVector
    N3-->>N1: Value + VersionVector
  
    N1->>N1: WaitForQuorum(R=2)
    N1->>N1: ResolveConflicts(VersionVectors)
    N1-->>C: Merged Result
```

#### 4.4.2 写操作流程

```mermaid
sequenceDiagram
    participant C as Client
    participant N1 as Node1(Coordinator)
    participant N2 as Node2
    participant N3 as Node3
  
    C->>N1: Put(key, value)
    N1->>N1: GetReplicas(key, N=3)
    N1->>N1: IncrementVersionVector()
  
    par 并行写入副本
        N1->>N1: Local Put
        N1->>N2: Internal Put
        N1->>N3: Internal Put
    end
  
    N2-->>N1: Success
    N3-->>N1: Success
  
    N1->>N1: WaitForQuorum(W=2)
    N1-->>C: Success Response
```

#### 4.4.3 Gossip 协议流程

```mermaid
sequenceDiagram
    participant N1 as Node1
    participant N2 as Node2
    participant N3 as Node3
  
    loop 每10秒
        N1->>N1: 选择随机节点
        N1->>N2: GossipMessage(NodeStates)
        N2->>N2: 合并节点状态
        N2-->>N1: GossipResponse(UpdatedStates)
        N1->>N1: 更新本地状态
    end
  
    Note over N1,N3: 所有节点最终达到状态一致
```

## 5. 非功能性设计

### 5.1 性能设计

#### 5.1.1 性能目标

- 读写延迟：P99 < 10ms
- 吞吐量：> 10,000 TPS
- 节点覆盖率：> 70%

#### 5.1.2 性能优化策略

- 连接池复用 gRPC 连接
- 异步发送使用 goroutine pool
- 内存存储减少 I/O 延迟
- 批量处理减少网络开销

### 5.2 可用性设计

#### 5.2.1 CAP 权衡

- 优先 AP (可用性和分区容忍)
- 使用最终一致性而非强一致性
- Quorum (N=3, R=2, W=2) 平衡一致性和可用性

#### 5.2.2 故障处理机制

- **节点故障检测**：Gossip 协议 + 心跳检测
- **Hinted Handoff**：故障节点消息临时存储
- **反熵修复**：周期性数据同步
- **版本向量冲突解决**：自动合并冲突版本

### 5.3 扩展性设计

#### 5.3.1 水平扩展

- 一致性哈希支持动态节点添加/移除
- 虚拟节点均衡数据分布
- 无主架构避免扩展瓶颈

#### 5.3.2 数据迁移

```mermaid
flowchart TD
    A[新节点加入] --> B[更新哈希环]
    B --> C[计算数据迁移范围]
    C --> D[从相邻节点复制数据]
    D --> E[验证数据完整性]
    E --> F[更新集群状态]
    F --> G[开始服务请求]
```

### 5.4 安全性设计

#### 5.4.1 认证机制

- 内部通信使用 Token 认证
- 客户端连接支持 TLS
- 配置文件敏感信息加密

#### 5.4.2 网络安全

- 公共端口 (50051) 仅暴露客户端接口
- 内部端口 (50052) 仅集群内部访问
- 防火墙规则限制端口访问

### 5.5 可维护性设计

#### 5.5.1 日志和监控

- 结构化日志使用 Logrus
- Prometheus 指标收集
- 分布式链路追踪

#### 5.5.2 配置管理

- YAML 配置文件
- 环境变量覆盖
- 热重载配置更新

## 6. 部署架构

### 6.1 物理架构图

```mermaid
C4Deployment
    title Deployment Diagram for HarmoniaKV

    Deployment_Node(dc1, "Data Center 1", "主数据中心") {
        Deployment_Node(k8s1, "Kubernetes Cluster", "容器编排") {
            Container(node1, "HarmoniaKV Node 1", "Pod")
            Container(node2, "HarmoniaKV Node 2", "Pod")
        }
        Deployment_Node(lb1, "Load Balancer", "负载均衡") {
            Container(nginx1, "Nginx", "反向代理")
        }
    }
  
    Deployment_Node(dc2, "Data Center 2", "备用数据中心") {
        Deployment_Node(k8s2, "Kubernetes Cluster", "容器编排") {
            Container(node3, "HarmoniaKV Node 3", "Pod")
        }
    }
  
    Rel(nginx1, node1, "gRPC", "50051")
    Rel(nginx1, node2, "gRPC", "50051")
    Rel(node1, node2, "Internal", "50052")
    Rel(node1, node3, "Internal", "50052")
    Rel(node2, node3, "Internal", "50052")
```

### 6.2 容器化部署

#### 6.2.1 Docker Compose 配置

```yaml
version: '3.8'
services:
  harmoniakv-node1:
    image: harmoniakv:latest
    ports:
      - "50051:50051"
      - "50052:50052"
    environment:
      - NODE_ID=node1
      - NODE_ADDR=harmoniakv-node1:50052
      - PEERS=harmoniakv-node2:50052,harmoniakv-node3:50052
    volumes:
      - ./config:/app/config
      - node1-data:/app/data

  harmoniakv-node2:
    image: harmoniakv:latest
    ports:
      - "50053:50051"
      - "50054:50052"
    environment:
      - NODE_ID=node2
      - NODE_ADDR=harmoniakv-node2:50052
      - PEERS=harmoniakv-node1:50052,harmoniakv-node3:50052

  harmoniakv-node3:
    image: harmoniakv:latest
    ports:
      - "50055:50051"
      - "50056:50052"
    environment:
      - NODE_ID=node3
      - NODE_ADDR=harmoniakv-node3:50052
      - PEERS=harmoniakv-node1:50052,harmoniakv-node2:50052

volumes:
  node1-data:
  node2-data:
  node3-data:
```

### 6.3 Kubernetes 部署

#### 6.3.1 StatefulSet 配置

```yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: harmoniakv
spec:
  serviceName: harmoniakv
  replicas: 3
  selector:
    matchLabels:
      app: harmoniakv
  template:
    metadata:
      labels:
        app: harmoniakv
    spec:
      containers:
      - name: harmoniakv
        image: harmoniakv:latest
        ports:
        - containerPort: 50051
          name: client
        - containerPort: 50052
          name: internal
        env:
        - name: NODE_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: PEERS
          value: "harmoniakv-0.harmoniakv:50052,harmoniakv-1.harmoniakv:50052,harmoniakv-2.harmoniakv:50052"
        volumeMounts:
        - name: data
          mountPath: /app/data
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
```

## 7. 容灾与高可用

### 7.1 故障场景分析

#### 7.1.1 节点故障

```mermaid
flowchart TD
    A[节点故障检测] --> B{故障类型}
    B -->|网络分区| C[继续服务可达节点]
    B -->|节点宕机| D[Hinted Handoff]
    B -->|磁盘故障| E[数据恢复]
  
    C --> F[等待网络恢复]
    D --> G[临时存储写入]
    E --> H[从副本恢复]
  
    F --> I[反熵同步]
    G --> J[故障节点恢复后移交]
    H --> I
    J --> I
    I --> K[数据一致性恢复]
```

#### 7.1.2 数据中心故障

- **跨数据中心部署**：至少 2 个数据中心
- **异步复制**：跨数据中心数据同步
- **自动故障转移**：DNS 切换到备用数据中心

### 7.2 恢复策略

#### 7.2.1 数据恢复流程

1. **检测数据不一致**：版本向量比较
2. **反熵修复**：Merkle 树对比
3. **冲突解决**：Last-Write-Wins + 版本向量
4. **数据验证**：校验和验证

#### 7.2.2 服务恢复时间目标

- **RTO (Recovery Time Objective)**：< 5 分钟
- **RPO (Recovery Point Objective)**：< 1 分钟
- **MTTR (Mean Time To Recovery)**：< 10 分钟

### 7.3 一致性权衡

- **最终一致性**：牺牲即时一致性换取高可用性
- **读写仲裁**：Quorum 机制平衡一致性和可用性
- **冲突解决**：版本向量自动合并冲突

## 8. 监控与运维

### 8.1 监控指标

#### 8.1.1 系统指标

- **QPS**：每秒查询数
- **延迟**：P50, P95, P99 响应时间
- **错误率**：4xx, 5xx 错误比例
- **可用性**：服务正常运行时间

#### 8.1.2 业务指标

- **数据分布**：各节点数据量分布
- **副本一致性**：版本向量差异
- **集群健康度**：活跃节点比例

### 8.2 告警策略

```mermaid
flowchart TD
    A[监控指标] --> B{阈值检查}
    B -->|正常| C[继续监控]
    B -->|警告| D[发送警告通知]
    B -->|严重| E[发送紧急告警]
  
    D --> F[运维人员处理]
    E --> G[自动故障转移]
    G --> H[通知运维团队]
  
    F --> I[问题解决]
    H --> I
    I --> C
```

### 8.3 运维工具

- **集群管理**：kubectl, docker-compose
- **监控面板**：Grafana 仪表板
- **日志分析**：ELK Stack
- **性能分析**：pprof, trace

## 9. 风险与应对

### 9.1 技术风险

| 风险项       | 影响程度 | 发生概率 | 应对措施         |
| ------------ | -------- | -------- | ---------------- |
| 热点键访问   | 高       | 中       | 虚拟节点负载均衡 |
| 版本向量爆炸 | 中       | 低       | 定期清理旧版本   |
| 网络分区     | 高       | 中       | Quorum 机制容错  |
| 数据倾斜     | 中       | 中       | 一致性哈希重平衡 |

### 9.2 运维风险

| 风险项       | 影响程度 | 发生概率 | 应对措施       |
| ------------ | -------- | -------- | -------------- |
| 配置错误     | 高       | 中       | 配置验证和回滚 |
| 版本升级失败 | 中       | 低       | 灰度发布和回滚 |
| 容量不足     | 高       | 中       | 自动扩容和监控 |
| 安全漏洞     | 高       | 低       | 安全扫描和更新 |

### 9.3 业务风险

| 风险项     | 影响程度 | 发生概率 | 应对措施       |
| ---------- | -------- | -------- | -------------- |
| 数据丢失   | 高       | 低       | 多副本和备份   |
| 服务不可用 | 高       | 中       | 高可用架构     |
| 性能下降   | 中       | 中       | 性能监控和优化 |
| 数据泄露   | 高       | 低       | 访问控制和加密 |

## 10. 附录

### 10.1 参考资料

- [Amazon Dynamo 论文](https://www.allthingsdistributed.com/files/amazon-dynamo-sosp2007.pdf)
- [Consistent Hashing 论文](https://dl.acm.org/doi/10.1145/258533.258660)
- [Vector Clocks 论文](https://en.wikipedia.org/wiki/Vector_clock)
- [Gossip Protocol 论文](https://www.cs.cornell.edu/home/<USER>/papers/flowgossip.pdf)

### 10.2 相关文档

- [消息流程详细设计](./message-flows.md)
- [传输层详细设计](./transport-layer-design.md)
- [实现规划文档](../implementation_plan.md)  
- [一致性哈希教程](../tutorials/consistent-hashing.md)
- [分布式锁教程](../tutorials/distributed-lock.md)
- [性能基准测试](../performance/harmoniakv-benchmarks.md)

### 10.3 术语对照表

| 英文术语              | 中文术语   | 说明           |
| --------------------- | ---------- | -------------- |
| Consistent Hashing    | 一致性哈希 | 数据分布算法   |
| Vector Clock          | 版本向量   | 分布式版本控制 |
| Quorum                | 仲裁       | 读写一致性机制 |
| Gossip Protocol       | 流言协议   | 集群状态传播   |
| Hinted Handoff        | 暗示移交   | 故障恢复机制   |
| Anti-entropy          | 反熵       | 数据修复机制   |
| Eventually Consistent | 最终一致性 | 一致性模型     |
| CAP Theorem           | CAP 定理   | 分布式系统理论 |
