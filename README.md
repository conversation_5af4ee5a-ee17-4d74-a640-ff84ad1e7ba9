# 分布式系统学习实验室 🚀

一个全面的分布式系统学习平台，通过实际代码展示现代分布式系统的设计模式和最佳实践。

[![Go Report Card](https://goreportcard.com/badge/github.com/yourusername/distributed-learning-lab)](https://goreportcard.com/report/github.com/yourusername/distributed-learning-lab)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Build Status](https://github.com/yourusername/distributed-learning-lab/workflows/CI/badge.svg)](https://github.com/yourusername/distributed-learning-lab/actions)

## 🎯 项目目标

- **教育导向**: 通过实际代码学习分布式系统核心概念
- **生产就绪**: 所有实现都遵循生产环境的最佳实践
- **渐进式学习**: 从基础模式到完整系统的学习路径
- **社区驱动**: 欢迎贡献新的模式实现和改进

## 📚 学习路径

### 初学者路径
1. **一致性哈希** → 理解数据分布
2. **分布式锁** → 掌握协调机制
3. **Raft算法** → 学习共识机制
4. **完整KV存储** → 构建真实系统

### 进阶路径
1. **性能调优** → 基准测试和优化
2. **监控告警** → 可观测性最佳实践
3. **故障注入** → 混沌工程测试
4. **扩展实现** → 添加新功能

## 🗂️ 项目结构

```
distributed-learning-lab/
├── examples/           # 完整示例应用
│   ├── harmoniakv/     # Dynamo风格的分布式KV存储
│   ├── chat-room/      # 基于Raft的聊天室
│   └── lock-service/   # 分布式锁服务
├── patterns/           # 设计模式实现
│   ├── consensus/      # 共识算法(Raft, Paxos)
│   ├── coordination/   # 协调服务(分布式锁,选举)
│   └── storage/        # 存储模式(一致性哈希,WAL)
├── pkg/               # 可复用的库
│   ├── wal/           # 预写日志
│   ├── hash/          # 一致性哈希
│   └── discovery/     # 服务发现
├── docs/              # 完整文档
│   ├── tutorials/     # 逐步教程
│   ├── architecture/  # 架构决策
│   └── performance/   # 性能基准
└── scripts/           # 工具脚本
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone https://github.com/yourusername/distributed-learning-lab.git
cd distributed-learning-lab

# 安装依赖
make deps

# 启动测试环境
make docker-up
```

### 2. 运行示例

```bash
# 构建所有示例
make build

# 运行分布式KV存储
make example-harmoniakv

# 运行分布式锁示例
make example-lock

# 运行聊天室示例
make example-chat
```

### 3. 运行测试

```bash
# 运行所有测试
make test

# 运行特定模式测试
go test ./patterns/consensus/...
go test ./patterns/coordination/...
```

## 📖 学习资源

### 核心概念
- [一致性哈希详解](docs/tutorials/consistent-hashing.md)
- [分布式锁实现](docs/tutorials/distributed-lock.md)
- [Raft算法实战](docs/tutorials/raft-consensus.md)
- [Dynamo架构解析](docs/tutorials/dynamo-architecture.md)

### 架构决策
- [ADR-001: 选择Raft而非Paxos](docs/architecture/adr-001-raft-vs-paxos.md)
- [ADR-002: Redis vs ETCD for分布式锁](docs/architecture/adr-002-distributed-lock-implementation.md)

### 性能基准
- [KV存储性能测试](docs/performance/harmoniakv-benchmarks.md)
- [锁服务性能对比](docs/performance/lock-performance.md)

## 🛠️ 技术栈

- **语言**: Go 1.21+
- **存储**: Redis, ETCD, BadgerDB
- **网络**: gRPC, Protobuf
- **监控**: Prometheus, Grafana
- **容器**: Docker, Docker Compose

## 🤝 贡献指南

我们欢迎所有形式的贡献！

- **报告Bug**: 使用GitHub Issues
- **新功能**: 查看[CONTRIBUTING.md](CONTRIBUTING.md)
- **文档改进**: 直接提交PR
- **问题讨论**: 开启GitHub Discussion

### 开发环境

```bash
# 一键设置开发环境
make dev-setup

# 代码格式化
make fmt

# 静态检查
make lint
```

## 📊 项目状态

| 组件 | 状态 | 文档 | 示例 | 测试 |
|------|------|------|------|------|
| 一致性哈希 | ✅ | ✅ | ✅ | ✅ |
| 分布式锁 | ✅ | ✅ | ✅ | ✅ |
| Raft算法 | ✅ | ✅ | ✅ | ✅ |
| Harmoniakv | ✅ | ✅ | ✅ | ✅ |
| Paxos算法 | 🚧 | ✅ | ⏳ | ✅ |
| 限流器 | ⏳ | ✅ | ⏳ | ⏳ |

- ✅ 已完成
- 🚧 开发中
- ⏳ 计划中

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- [MIT 6.824](https://pdos.csail.mit.edu/6.824/) - 分布式系统课程
- [Distributed Systems](https://book.mixu.net/distsys/) - 分布式系统书籍
- [Raft论文](https://raft.github.io/raft.pdf) - Raft共识算法

## 📞 联系方式

- 项目维护: [yourusername](https://github.com/yourusername)
- 讨论群组: [GitHub Discussions](https://github.com/yourusername/distributed-learning-lab/discussions)
- 问题反馈: [GitHub Issues](https://github.com/yourusername/distributed-learning-lab/issues)

---

**⭐ 如果这个项目对你有帮助，请给个星标支持！**