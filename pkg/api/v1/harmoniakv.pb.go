// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: pkg/api/v1/harmoniakv.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Object struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           []byte                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value         []byte                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	Vector        map[string]uint64      `protobuf:"bytes,3,rep,name=vector,proto3" json:"vector,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Object) Reset() {
	*x = Object{}
	mi := &file_pkg_api_v1_harmoniakv_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Object) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Object) ProtoMessage() {}

func (x *Object) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_api_v1_harmoniakv_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Object.ProtoReflect.Descriptor instead.
func (*Object) Descriptor() ([]byte, []int) {
	return file_pkg_api_v1_harmoniakv_proto_rawDescGZIP(), []int{0}
}

func (x *Object) GetKey() []byte {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *Object) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *Object) GetVector() map[string]uint64 {
	if x != nil {
		return x.Vector
	}
	return nil
}

type GetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           []byte                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRequest) Reset() {
	*x = GetRequest{}
	mi := &file_pkg_api_v1_harmoniakv_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRequest) ProtoMessage() {}

func (x *GetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_api_v1_harmoniakv_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRequest.ProtoReflect.Descriptor instead.
func (*GetRequest) Descriptor() ([]byte, []int) {
	return file_pkg_api_v1_harmoniakv_proto_rawDescGZIP(), []int{1}
}

func (x *GetRequest) GetKey() []byte {
	if x != nil {
		return x.Key
	}
	return nil
}

type GetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Objects       []*Object              `protobuf:"bytes,1,rep,name=objects,proto3" json:"objects,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetResponse) Reset() {
	*x = GetResponse{}
	mi := &file_pkg_api_v1_harmoniakv_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResponse) ProtoMessage() {}

func (x *GetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_api_v1_harmoniakv_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResponse.ProtoReflect.Descriptor instead.
func (*GetResponse) Descriptor() ([]byte, []int) {
	return file_pkg_api_v1_harmoniakv_proto_rawDescGZIP(), []int{2}
}

func (x *GetResponse) GetObjects() []*Object {
	if x != nil {
		return x.Objects
	}
	return nil
}

type PutRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           []byte                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value         []byte                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	Vector        map[string]uint64      `protobuf:"bytes,3,rep,name=vector,proto3" json:"vector,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PutRequest) Reset() {
	*x = PutRequest{}
	mi := &file_pkg_api_v1_harmoniakv_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PutRequest) ProtoMessage() {}

func (x *PutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_api_v1_harmoniakv_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PutRequest.ProtoReflect.Descriptor instead.
func (*PutRequest) Descriptor() ([]byte, []int) {
	return file_pkg_api_v1_harmoniakv_proto_rawDescGZIP(), []int{3}
}

func (x *PutRequest) GetKey() []byte {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *PutRequest) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *PutRequest) GetVector() map[string]uint64 {
	if x != nil {
		return x.Vector
	}
	return nil
}

type PutResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PutResponse) Reset() {
	*x = PutResponse{}
	mi := &file_pkg_api_v1_harmoniakv_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PutResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PutResponse) ProtoMessage() {}

func (x *PutResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_api_v1_harmoniakv_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PutResponse.ProtoReflect.Descriptor instead.
func (*PutResponse) Descriptor() ([]byte, []int) {
	return file_pkg_api_v1_harmoniakv_proto_rawDescGZIP(), []int{4}
}

func (x *PutResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_pkg_api_v1_harmoniakv_proto protoreflect.FileDescriptor

const file_pkg_api_v1_harmoniakv_proto_rawDesc = "" +
	"\n" +
	"\x1bpkg/api/v1/harmoniakv.proto\"\x98\x01\n" +
	"\x06Object\x12\x10\n" +
	"\x03key\x18\x01 \x01(\fR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\fR\x05value\x12+\n" +
	"\x06vector\x18\x03 \x03(\v2\x13.Object.VectorEntryR\x06vector\x1a9\n" +
	"\vVectorEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x04R\x05value:\x028\x01\"\x1e\n" +
	"\n" +
	"GetRequest\x12\x10\n" +
	"\x03key\x18\x01 \x01(\fR\x03key\"0\n" +
	"\vGetResponse\x12!\n" +
	"\aobjects\x18\x01 \x03(\v2\a.ObjectR\aobjects\"\xa0\x01\n" +
	"\n" +
	"PutRequest\x12\x10\n" +
	"\x03key\x18\x01 \x01(\fR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\fR\x05value\x12/\n" +
	"\x06vector\x18\x03 \x03(\v2\x17.PutRequest.VectorEntryR\x06vector\x1a9\n" +
	"\vVectorEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x04R\x05value:\x028\x01\"'\n" +
	"\vPutResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess2P\n" +
	"\n" +
	"Harmoniakv\x12 \n" +
	"\x03Get\x12\v.GetRequest\x1a\f.GetResponse\x12 \n" +
	"\x03Put\x12\v.PutRequest\x1a\f.PutResponseB,Z*distributed-learning-lab/harmoniakv/api/v1b\x06proto3"

var (
	file_pkg_api_v1_harmoniakv_proto_rawDescOnce sync.Once
	file_pkg_api_v1_harmoniakv_proto_rawDescData []byte
)

func file_pkg_api_v1_harmoniakv_proto_rawDescGZIP() []byte {
	file_pkg_api_v1_harmoniakv_proto_rawDescOnce.Do(func() {
		file_pkg_api_v1_harmoniakv_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pkg_api_v1_harmoniakv_proto_rawDesc), len(file_pkg_api_v1_harmoniakv_proto_rawDesc)))
	})
	return file_pkg_api_v1_harmoniakv_proto_rawDescData
}

var file_pkg_api_v1_harmoniakv_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_pkg_api_v1_harmoniakv_proto_goTypes = []any{
	(*Object)(nil),      // 0: Object
	(*GetRequest)(nil),  // 1: GetRequest
	(*GetResponse)(nil), // 2: GetResponse
	(*PutRequest)(nil),  // 3: PutRequest
	(*PutResponse)(nil), // 4: PutResponse
	nil,                 // 5: Object.VectorEntry
	nil,                 // 6: PutRequest.VectorEntry
}
var file_pkg_api_v1_harmoniakv_proto_depIdxs = []int32{
	5, // 0: Object.vector:type_name -> Object.VectorEntry
	0, // 1: GetResponse.objects:type_name -> Object
	6, // 2: PutRequest.vector:type_name -> PutRequest.VectorEntry
	1, // 3: Harmoniakv.Get:input_type -> GetRequest
	3, // 4: Harmoniakv.Put:input_type -> PutRequest
	2, // 5: Harmoniakv.Get:output_type -> GetResponse
	4, // 6: Harmoniakv.Put:output_type -> PutResponse
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_pkg_api_v1_harmoniakv_proto_init() }
func file_pkg_api_v1_harmoniakv_proto_init() {
	if File_pkg_api_v1_harmoniakv_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pkg_api_v1_harmoniakv_proto_rawDesc), len(file_pkg_api_v1_harmoniakv_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pkg_api_v1_harmoniakv_proto_goTypes,
		DependencyIndexes: file_pkg_api_v1_harmoniakv_proto_depIdxs,
		MessageInfos:      file_pkg_api_v1_harmoniakv_proto_msgTypes,
	}.Build()
	File_pkg_api_v1_harmoniakv_proto = out.File
	file_pkg_api_v1_harmoniakv_proto_goTypes = nil
	file_pkg_api_v1_harmoniakv_proto_depIdxs = nil
}
