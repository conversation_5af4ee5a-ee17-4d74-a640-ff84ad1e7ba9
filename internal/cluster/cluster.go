package cluster

import (
	"context"
	"crypto/sha256"
	"encoding/binary"
	"encoding/json"
	"errors"
	"sort"
	"strconv"
	"sync"

	"github.com/sirupsen/logrus"
	internal_api "github.com/suohailong/harmoniakv/internal/api/v1"
	"github.com/suohailong/harmoniakv/internal/config"
	"github.com/suohailong/harmoniakv/internal/transport"
)

/*
*******
需要实现的功能:
数据分片
数据复制
一致性
不一致的解决方案
失效处理
********
*/
// 新增：Cluster 接口和 GossipStateMessage 定义
type GossipStateMessage struct {
	Source     *Node
	NodeStates map[string]*Node
}

type Cluster interface {
	Start()
	HandleGossipMessage(*GossipStateMessage) // 使用 GossipStateMessage
	AddNode(*Node)
	RemoveNode(*Node)
	GetReplicas([]byte, int) []*Node
	GetNode([]byte) *Node
	NodeLen() int
	HandleNodeJoin(ctx context.Context, msg *internal_api.InternalRequest) error
	HandleNodeLeave(ctx context.Context, msg *internal_api.InternalRequest) error
	HandleAntiEntropy(ctx context.Context, msg *internal_api.InternalRequest) error
	HandleHintedHandoff(ctx context.Context, msg *internal_api.InternalRequest) error
	HandleQuorumAck(ctx context.Context, msg *internal_api.InternalRequest) error
	HandleVersionMerge(ctx context.Context, msg *internal_api.InternalRequest) error
	HandleClusterQuery(ctx context.Context, msg *internal_api.InternalRequest) error
	HandleKvCommand(ctx context.Context, msg *internal_api.InternalRequest) error
}

type defaultCluster struct {
	sync.RWMutex
	// 当前node
	currentNode *Node

	vnodesMap map[uint32]*Node
	// hash环
	vnodes []uint32
	// 物理节点
	pnodes []*Node
	// 随机发送信息的node个数
	randomNode int
	// 虚拟节点个数
	vCount int
	// 副本数
	replica int
	trans   transport.Transporter
}

func (c *defaultCluster) AddNode(n *Node) {
	c.Lock()
	defer c.Unlock()
	c.pnodes = append(c.pnodes, n)
	for i := 0; i < c.vCount; i++ {
		vn := n.GetId() + "-" + strconv.Itoa(i)
		hash := c.hash(vn)
		c.vnodesMap[hash] = n
		c.vnodes = append(c.vnodes, hash)
	}
	sort.Slice(c.vnodes, func(i, j int) bool {
		// 从小到大排列
		return c.vnodes[i] < c.vnodes[j]
	})
	sort.Slice(c.pnodes, func(i, j int) bool {
		// 从小到大排列
		return c.pnodes[i].GetId() < c.pnodes[j].GetId()
	})
}

func (c *defaultCluster) RemoveNode(n *Node) {
	c.Lock()
	defer c.Unlock()
	for i := 0; i < c.vCount; i++ {
		vn := n.GetId() + "-" + strconv.Itoa(i)
		targetHash := c.hash(vn)
		delete(c.vnodesMap, targetHash)
		for index, hash := range c.vnodes {
			if hash == targetHash {
				c.vnodes = append(c.vnodes[:index], c.vnodes[index+1:]...)
			}
		}
	}
	// 要求待搜索的切片已经按照升序排列
	index := sort.Search(len(c.pnodes), func(i int) bool {
		// 找到第一个大于等于node的节点
		return c.pnodes[i].GetId() >= n.GetId()
	})
	c.pnodes = append(c.pnodes[:index], c.pnodes[index+1:]...)
}

func (c *defaultCluster) GetReplicas(key []byte, count int) (target []*Node) {
	c.RLock()
	defer c.RUnlock()
	hash := c.hash(string(key))
	index := sort.Search(len(c.vnodes), func(i int) bool {
		return c.vnodes[i] >= hash
	})

	for _, hash := range c.vnodes[index:] {
		target = append(target, c.vnodesMap[hash])
		// TODO: 跳过相同的物理机器
		// TODO: 跳过不可达的节点
		if len(target) == count {
			break
		}
	}
	return
}

func (c *defaultCluster) GetNode(key []byte) *Node {
	c.RLock()
	defer c.RUnlock()
	if c.NodeLen() == 0 {
		return nil
	}
	hash := c.hash(string(key))
	index := sort.Search(len(c.vnodes), func(i int) bool {
		return c.vnodes[i] >= hash
	})
	targetHash := c.vnodes[index]
	return c.vnodesMap[targetHash]
}

func (c *defaultCluster) NodeLen() int {
	c.RLock()
	defer c.RUnlock()
	return len(c.pnodes)
}

func (d *defaultCluster) hash(key string) uint32 {
	hash := sha256.Sum256([]byte(key))
	return binary.BigEndian.Uint32(hash[:4])
}

func (c *defaultCluster) Start() {
	c.GossipStart()
}

// HandleNodeJoin 处理 NodeJoin 消息
func (c *defaultCluster) HandleNodeJoin(ctx context.Context, req *internal_api.InternalRequest) error {
	var nodeData struct {
		ID      string
		Address string
	}
	if req.Data != nil && req.Data.TypeUrl == "application/json" {
		var data map[string]interface{}
		if err := json.Unmarshal(req.Data.Value, &data); err != nil {
			return err
		}
		if id, ok := data["ID"].(string); ok {
			nodeData.ID = id
		}
		if addr, ok := data["Address"].(string); ok {
			nodeData.Address = addr
		}
	} else {
		return errors.New("invalid data for NodeJoin")
	}
	node := NewNode(nodeData.ID, nodeData.Address, c, c.trans)
	c.AddNode(node)
	logrus.Infof("Node joined: %s", nodeData.ID)
	return nil
}

// HandleNodeLeave 处理 NodeLeave 消息
func (c *defaultCluster) HandleNodeLeave(ctx context.Context, req *internal_api.InternalRequest) error {
	var nodeID string
	if req.Data != nil && req.Data.TypeUrl == "application/json" {
		if err := json.Unmarshal(req.Data.Value, &nodeID); err != nil {
			return err
		}
	} else {
		return errors.New("invalid data for NodeLeave")
	}
	node := &Node{ID: nodeID}
	c.RemoveNode(node)
	logrus.Infof("Node left: %s", nodeID)
	return nil
}

// HandleAntiEntropy 处理 AntiEntropy 消息
func (c *defaultCluster) HandleAntiEntropy(ctx context.Context, req *internal_api.InternalRequest) error {
	// 假设 data 是需要同步的状态 map
	if req.Data != nil && req.Data.TypeUrl == "application/json" {
		var data map[string]interface{}
		if err := json.Unmarshal(req.Data.Value, &data); err != nil {
			return err
		}
		// 实现 anti-entropy 逻辑，如合并状态
	} else {
		return errors.New("invalid data for AntiEntropy")
	}
	logrus.Info("Handled AntiEntropy")
	return nil
}

// HandleHintedHandoff 处理 HintedHandoff 消息
func (c *defaultCluster) HandleHintedHandoff(ctx context.Context, req *internal_api.InternalRequest) error {
	// 假设 data 是 hint 数据
	if req.Data != nil && req.Data.TypeUrl == "application/json" {
		var data map[string]interface{}
		if err := json.Unmarshal(req.Data.Value, &data); err != nil {
			return err
		}
		// 实现 hinted handoff 逻辑
	} else {
		return errors.New("invalid data for HintedHandoff")
	}
	logrus.Info("Handled HintedHandoff")
	return nil
}

// HandleQuorumAck 处理 QuorumAck 消息
func (c *defaultCluster) HandleQuorumAck(ctx context.Context, req *internal_api.InternalRequest) error {
	// 假设 data 是 ack 数据
	if req.Data != nil && req.Data.TypeUrl == "application/json" {
		var data map[string]interface{}
		if err := json.Unmarshal(req.Data.Value, &data); err != nil {
			return err
		}
		// 实现 quorum ack 逻辑
	} else {
		return errors.New("invalid data for QuorumAck")
	}
	logrus.Info("Handled QuorumAck")
	return nil
}

// HandleVersionMerge 处理 VersionMerge 消息
func (c *defaultCluster) HandleVersionMerge(ctx context.Context, req *internal_api.InternalRequest) error {
	// 假设 data 是版本合并数据
	if req.Data != nil && req.Data.TypeUrl == "application/json" {
		var data map[string]interface{}
		if err := json.Unmarshal(req.Data.Value, &data); err != nil {
			return err
		}
		// 实现版本合并逻辑
	} else {
		return errors.New("invalid data for VersionMerge")
	}
	logrus.Info("Handled VersionMerge")
	return nil
}

// HandleClusterQuery 处理 ClusterQuery 消息
func (c *defaultCluster) HandleClusterQuery(ctx context.Context, req *internal_api.InternalRequest) error {
	// 假设 data 是查询参数
	if req.Data != nil && req.Data.TypeUrl == "application/json" {
		var data map[string]interface{}
		if err := json.Unmarshal(req.Data.Value, &data); err != nil {
			return err
		}
		// 实现集群查询逻辑
	} else {
		return errors.New("invalid data for ClusterQuery")
	}
	logrus.Info("Handled ClusterQuery")
	return nil
}

// HandleKvCommand 处理 KvCommand 消息
func (c *defaultCluster) HandleKvCommand(ctx context.Context, req *internal_api.InternalRequest) error {
	// 假设 data 是 KV 命令
	if req.Data != nil && req.Data.TypeUrl == "application/json" {
		var data map[string]interface{}
		if err := json.Unmarshal(req.Data.Value, &data); err != nil {
			return err
		}
		// 实现 KV 命令处理逻辑
	} else {
		return errors.New("invalid data for KvCommand")
	}
	logrus.Info("Handled KvCommand")
	return nil
}

type Option func(*defaultCluster)

func WithTrans(trans transport.Transporter) Option {
	return func(c *defaultCluster) {
		c.trans = trans
	}
}

// New 创建 Cluster 实例，使用选项模式配置参数
// 示例使用: cluster.New(WithVnodeCount(20), WithReplicas(3))
func New(opts ...Option) Cluster {
	cc := &defaultCluster{
		vnodesMap:  make(map[uint32]*Node),
		vnodes:     make([]uint32, 0),
		pnodes:     make([]*Node, 0),
		randomNode: config.RandomNode(), // 使用常量作为默认值
		vCount:     config.VnodeCount(), // 使用常量作为默认值
		replica:    config.Replicas(),   // 使用常量作为默认值
	}
	for _, opt := range opts {
		opt(cc)
	}

	return cc
}
