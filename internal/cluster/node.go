package cluster

import (
	"context" // 新增：用于 ctx 传递
	"encoding/json"
	"errors"
	"fmt" // 新增：用于 Sprintf

	"github.com/suohailong/harmoniakv/internal/storage"
	"github.com/suohailong/harmoniakv/internal/version"
	"google.golang.org/protobuf/types/known/anypb"

	"github.com/sirupsen/logrus"
	internal_api "github.com/suohailong/harmoniakv/internal/api/v1"
	"github.com/suohailong/harmoniakv/internal/config"
	"github.com/suohailong/harmoniakv/internal/transport"
	v1 "github.com/suohailong/harmoniakv/pkg/api/v1"
)

// 导入结束

const (
	ONLINE  = 1 // 0
	OFFLINE = -1
)

// Node 结构体使用 Cluster
type Node struct {
	ID      string
	Address string
	// TODO: 这里会一直增长吗
	Counter   uint64
	HeartTime int64
	State     int8
	SendTime  int64

	Store storage.Storage

	// 在 Node 结构体中，将 cluster 类型改为 Cluster（现在在本包）
	cluster Cluster

	// 1. 在 Node 结构体中添加 Transporter 字段
	trans transport.Transporter
}

const (
	GET = 1
	PUT = 2
)

type KvCommand struct {
	Command uint32
	Key     []byte
	Value   version.Value
}

// NewNode：接受 Cluster 接口注入
func NewNode(id string, address string, cl Cluster, trans transport.Transporter) *Node {
	return &Node{
		ID:        id,
		Address:   address,
		Counter:   0,
		State:     ONLINE,
		HeartTime: 0,
		SendTime:  0,
		cluster:   cl, // 注入
		trans:     trans,
	}
}

func (n *Node) GetId() string {
	return n.ID
}

// TODO: 这里返回值暂且不知道填什么
//
//go:noinline
func (n *Node) HandleCommand(cmd *KvCommand) (interface{}, error) {
	logrus.Debugf("handle local msg: %v", cmd)
	if cmd.Command == GET {
		// 3. 从本地数据库读取该key的所有版本
		// n.store.Get(cmd.Key)
		logrus.Debugf("get key: %s", cmd.Key)
		// 4. 将所有版本返回给协调节点
		return nil, nil
	} else if cmd.Command == PUT {
		// 2. 生成该key对应的版本向量, 写入本地数据库
		cmd.Value.VersionVector.Increment(n.ID)
		// 3. 写入对应的值到本地
		n.Store.Put(cmd.Key, cmd.Value)
		return nil, nil
	}
	return nil, nil
}

// 新增：协调读请求（从 Coordinator.HandleGet 迁移，添加 ctx，修复参数缺失）
func (n *Node) CoordinateGet(ctx context.Context, key []byte) ([]*v1.Object, error) {
	// 1. 协调节点从该key的首选列表中排名最高的N个可达节点请求该key的所有数据版本
	nodes := n.cluster.GetReplicas(key, config.Replicas())
	// 2. 判断自己是否为首选节点,如果不是直接转发请求,到指定节点
	index := n.isInPrimaryList(nodes)
	if index < 0 {
		// 选择第一转发请求（修复：添加完整 KvCommand）
		primaryNode := nodes[0]
		msg, err := json.Marshal(&KvCommand{Command: GET, Key: key})
		if err != nil {
			return nil, err
		}
		dataAny := &anypb.Any{
			TypeUrl: "application/json",
			Value:   msg,
		}
		_, err = n.trans.Send(ctx, &internal_api.InternalRequest{
			Token:       config.InternalToken(),
			MessageType: internal_api.MessageType_MessageTypeKvCommand,
			From:        n.ID,
			To:          primaryNode.ID,
			Data:        dataAny,
		})
		if err != nil {
			return nil, err
		}
		// TODO: 处理转发响应（原代码中无返回，这里假设转发后返回空；后续完善）
		return []*v1.Object{}, nil
	}

	// 发送到 replicas
	respsCh := make(chan []byte, len(nodes))
	for i := 0; i < len(nodes); i++ {
		if nodes[i].ID == n.ID {
			resp, _ := n.HandleCommand(&KvCommand{Command: GET, Key: key}) // 处理为 []byte
			respsCh <- []byte(fmt.Sprint(resp))                            // 示例
			continue
		}
		msg, err := json.Marshal(&KvCommand{Command: GET, Key: key})
		if err != nil {
			continue
		}
		dataAny := &anypb.Any{
			TypeUrl: "application/json",
			Value:   msg,
		}
		n.trans.AsyncSend(ctx, &internal_api.InternalRequest{
			Token:       config.InternalToken(),
			MessageType: internal_api.MessageType_MessageTypeKvCommand,
			From:        n.ID,
			To:          nodes[i].ID,
			Data:        dataAny,
		})
	}
	// 收集
	resps := []interface{}{}
	for i := 0; i < config.ReadConcern(); i++ {
		respBytes := <-respsCh
		var resp interface{}
		json.Unmarshal(respBytes, &resp)
		resps = append(resps, resp)
	}

	// 4. 如果协调节点最终搜集到多个数据版本,它将返回它认为因果无关的版本
	logrus.Infof("get %s, resps: %v", key, resps)
	return []*v1.Object{}, nil // 原代码中返回空，待实现版本合并
}

// 新增：协调写请求（从 Coordinator.HandlePut 迁移，添加 ctx，修复参数缺失和本地写入）
func (n *Node) CoordinatePut(ctx context.Context, key []byte, value *version.Value) error {
	// 获取节点及副本，并跳过那些访问不到的节点
	nodes := n.cluster.GetReplicas(key, config.Replicas())
	if len(nodes) < config.Replicas() {
		// 可用节点小于replicas
		// 集群不可用（原代码无具体错误，添加简单返回）
		return errors.New("insufficient replicas available")
	}
	// 1. 判断自己是否为首选节点,如果不是直接转发请求,到指定节点
	index := n.isInPrimaryList(nodes)
	if index < 0 {
		// 选择第一转发请求（修复：添加完整 KvCommand）
		primaryNode := nodes[0]
		msg, err := json.Marshal(&KvCommand{Command: PUT, Key: key, Value: *value})
		if err != nil {
			return err
		}
		dataAny := &anypb.Any{
			TypeUrl: "application/json",
			Value:   msg,
		}
		respBytes, err := n.trans.Send(ctx, &internal_api.InternalRequest{
			Token:       config.InternalToken(),
			MessageType: internal_api.MessageType_MessageTypeKvCommand,
			From:        n.ID,
			To:          primaryNode.ID,
			Data:        dataAny,
		})
		if err != nil {
			logrus.Errorf("send to primary node error: %v", err)
			return err
		}
		// 使用 respBytes
		var resp interface{}
		if respBytes.Data != nil && respBytes.Data.TypeUrl == "application/json" {
			json.Unmarshal(respBytes.Data.Value, &resp) // 从 protobuf Any 中提取
		}
		// TODO: 反序列化 respBytes 到实际响应
		return nil // 转发后返回（原代码中无进一步处理）
	}
	// 2. 生成该key对应的版本向量, 写入本地数据库（修复：对本地节点调用 HandleCommand 而非 Send）
	var acceptValue interface{}
	var err error
	if nodes[index].ID == n.ID { // 本地检查
		acceptValue, err = n.HandleCommand(&KvCommand{
			Command: PUT,
			Key:     key,
			Value:   *value,
		})
	} else {
		msg, err := json.Marshal(&KvCommand{Command: PUT, Key: key, Value: *value})
		if err != nil {
			return err
		}
		dataAny := &anypb.Any{
			TypeUrl: "application/json",
			Value:   msg,
		}
		respBytes, err := n.trans.Send(ctx, &internal_api.InternalRequest{
			Token:       config.InternalToken(),
			MessageType: internal_api.MessageType_MessageTypeKvCommand,
			From:        n.ID,
			To:          nodes[index].ID,
			Data:        dataAny,
		})
		if err != nil {
			logrus.Errorf("write to node error: %v", err)
			return err
		}
		// 处理 messageResponse.Data
		// 例如：
		// if respBytes != nil && respBytes.Data != nil {
		//     json.Unmarshal(respBytes.Data.([]byte), &acceptValue)
		// }
		if respBytes != nil && respBytes.Data != nil && respBytes.Data.TypeUrl == "application/json" {
			json.Unmarshal(respBytes.Data.Value, &acceptValue)
		}
	}
	if err != nil {
		logrus.Errorf("write to node error: %v", err)
		return err
	}

	otherNodes := []*Node{}
	otherNodes = append(otherNodes, nodes[:index]...)
	otherNodes = append(otherNodes, nodes[index+1:]...)
	// 3. 发送给其他N-1个可达的副本. 不可达的副本采用Hinted Handoff的方式实现副本一致性（修复：添加完整 KvCommand）
	for i := 0; i < len(otherNodes); i++ {
		msg, err := json.Marshal(acceptValue) // 使用 acceptValue
		if err != nil {
			continue
		}
		dataAny := &anypb.Any{
			TypeUrl: "application/json",
			Value:   msg,
		}
		n.trans.AsyncSend(ctx, &internal_api.InternalRequest{
			Token:       config.InternalToken(),
			MessageType: internal_api.MessageType_MessageTypeKvCommand,
			From:        n.ID,
			To:          otherNodes[i].ID,
			Data:        dataAny,
		})
	}
	n.trans.WaitResponses(ctx, int(config.ReadConcern()))
	// 4. 等待w个个响应, 如果收到的响应少于w个,则重试,直到收到w个响应（原代码无重试，保持不变）

	// 5. 返回客户端（原代码结束）

	return nil
}

// 辅助方法：不变
func (n *Node) isInPrimaryList(replicas []*Node) int {
	for index, nd := range replicas {
		if nd.GetId() == n.ID {
			return index
		}
	}
	return -1
}
