# Harmoniakv v1 重新规划实现计划

## 重新评估：当前完成度约 45%

### 已完成的核心功能 ✅
- **架构框架**：90% - 模块化设计完整
- **集群管理**：85% - 一致性哈希 + Gossip 协议
- **Quorum 机制**：75% - 读写仲裁逻辑已实现
- **配置管理**：80% - 完整的配置系统
- **服务器框架**：60% - gRPC 双端口架构

### 关键缺失功能 ❌
- **传输层实现**：10% - 接口定义了但未实现
- **存储引擎**：70% - Get 方法缺失
- **版本冲突解决**：30% - 基础结构有，算法缺失
- **故障处理**：5% - Hinted Handoff 和反熵缺失

## 基于现状的 3 阶段实现计划

### 🚀 阶段 1: 核心功能补齐（第 1-4 周）
**目标**：让现有 Quorum 机制真正工作起来

#### 第 1 周：传输层实现（最高优先级）
**为什么优先**：现有 Quorum 逻辑依赖传输层，这是瓶颈

**任务清单**：
- [x] ✅ 实现 `internal/transport/transport.go` 基础功能
  - [x] ✅ `Send(address, request)` - 同步发送
  - [x] ✅ `AsyncSend(address, request)` - 异步发送  
  - [x] ⚠️ `WaitResp(count)` - 等待指定数量响应（有Bug需修复）
- [x] ✅ 实现连接管理功能
  - [x] ✅ gRPC 连接复用（LRU缓存）
  - [ ] ❌ 连接健康检查
- [ ] ❌ 实现响应收集和超时处理
  - [ ] 🔥 修复 `WaitResponses` 的 ID 映射问题（关键Bug）
  - [ ] 添加独立的响应收集器
  - [ ] 添加细粒度超时控制机制
- [ ] 📋 补充任务（根据检查发现）
  - [ ] 实现连接失败重试机制
  - [ ] 添加不可用节点标记
  - [ ] 完善 `transport_test.go` 测试覆盖
  - [ ] 按计划重构为多文件结构（可选，低优先级）

**里程碑**：节点间通信完全可用，Quorum 机制开始工作

**当前状态 (完成度 62%)**：
- ✅ 基础的同步/异步发送功能已实现
- ✅ gRPC 连接复用机制已完成
- ⚠️ 响应追踪有关键Bug，影响 Quorum 正常工作
- ❌ 缺少连接健康检查，可能导致请求超时

#### 第 2 周：存储引擎完善
**为什么重要**：读取功能是 KV 存储的基础

**任务清单**：
- [ ] 实现 `storage.Get()` 方法
  - 支持多版本检索
  - 版本向量序列化/反序列化
- [ ] 完善 `storage.Put()` 方法
  - 确保版本向量正确存储
- [ ] 添加 `storage.GetAll()` 和 `storage.Close()`

**里程碑**：完整的读写功能，支持版本向量

#### 第 3 周：版本冲突解决
**为什么关键**：多副本数据一致性的核心

**任务清单**：
- [ ] 实现 `internal/version/conflict_resolver.go`
  - 版本向量比较算法
  - 冲突检测逻辑
  - Last-Write-Wins 策略
- [ ] 实现 `internal/version/vector_clock.go`
  - 版本向量合并
  - 因果关系判断
- [ ] 集成到 `CoordinateGet` 中的响应合并

**里程碑**：自动冲突解决，数据一致性保证

#### 第 4 周：协调器逻辑完善
**为什么必要**：将前 3 周成果整合成完整流程

**任务清单**：
- [ ] 完善 `CoordinateGet` 响应处理
  - 多版本数据合并
  - 错误处理和重试
- [ ] 完善 `CoordinatePut` 写入流程
  - 版本向量递增
  - 写入确认机制
- [ ] 添加超时和错误处理

**里程碑**：完整的分布式 KV 读写功能

### 🛡️ 阶段 2: 可靠性增强（第 5-8 周）
**目标**：添加故障容错和数据持久性

#### 第 5-6 周：Hinted Handoff
**任务清单**：
- [ ] 实现 `internal/handoff/hint_store.go`
- [ ] 实现 `internal/handoff/handoff_manager.go`
- [ ] 集成到写入流程中

#### 第 7-8 周：WAL 集成
**任务清单**：
- [ ] 实现 `internal/wal/wal.go`
- [ ] 实现崩溃恢复机制
- [ ] 集成到存储引擎

### 🔧 阶段 3: 生产就绪（第 9-12 周）
**目标**：监控、运维和性能优化

#### 第 9-10 周：监控和认证
**任务清单**：
- [ ] 实现 `internal/auth/token.go`
- [ ] 实现 `internal/metrics/prometheus.go`
- [ ] 添加健康检查接口

#### 第 11-12 周：反熵和优化
**任务清单**：
- [ ] 实现 `internal/anti_entropy/anti_entropy.go`
- [ ] 性能优化和压力测试
- [ ] 文档完善

## 每周详细实施计划

### 第 1 周：传输层实现（关键周）

#### 周一-周二：gRPC 传输核心
```go
// 目标：实现基础的 Send 方法
type grpcTransport struct {
    clients map[string]v1.HarmoniakVClient
    mutex   sync.RWMutex
}

func (t *grpcTransport) Send(ctx context.Context, address string, request []byte) ([]byte, error) {
    // 实现同步发送逻辑
}
```

#### 周三-周四：异步发送和响应收集
```go
// 目标：实现 AsyncSend 和 WaitResp
func (t *grpcTransport) AsyncSend(ctx context.Context, address string, request []byte) {
    // 实现异步发送
}

func (t *grpcTransport) WaitResp(count int) [][]byte {
    // 实现响应等待
}
```

#### 周五：连接池和测试
- 实现连接池管理
- 编写传输层测试
- 集成测试验证

**周末验证**：运行完整的读写流程，确保 Quorum 机制工作

### 第 2 周：存储引擎完善

#### 周一-周二：Get 方法实现
```go
// 目标：支持多版本检索
func (d *defaultStorage) Get(key []byte) []*version.Value {
    // 实现多版本数据检索
}
```

#### 周三-周四：存储优化
- 完善 Put 方法的版本向量处理
- 添加 GetAll 和 Close 方法
- 存储性能优化

#### 周五：存储测试
- 多版本存储测试
- 并发读写测试

### 第 3 周：版本冲突解决

#### 周一-周二：冲突检测
```go
// 目标：版本向量比较
func (r *ConflictResolver) DetectConflicts(values []*version.Value) bool {
    // 实现冲突检测逻辑
}
```

#### 周三-周四：冲突解决
```go
// 目标：自动合并冲突版本
func (r *ConflictResolver) ResolveConflicts(values []*version.Value) []*version.Value {
    // 实现冲突解决算法
}
```

#### 周五：集成测试
- 多节点冲突场景测试
- 版本向量正确性验证

### 第 4 周：协调器完善

#### 周一-周二：读协调完善
- 集成冲突解决到 CoordinateGet
- 添加读取超时处理

#### 周三-周四：写协调完善  
- 完善 CoordinatePut 的确认机制
- 添加写入重试逻辑

#### 周五：端到端测试
- 完整的分布式读写测试
- 故障场景测试

## 关键里程碑和验收标准

### 第 1 周里程碑：传输层可用
**验收标准**：
- [x] ✅ 3 节点集群能够相互通信（基础功能已实现）
- [ ] ⚠️ Quorum 读写不再 panic（响应追踪Bug待修复）
- [ ] ❌ 基础的 Get/Put 操作成功（依赖响应追踪修复）

**当前阻塞问题**：
- 🔥 `WaitResponses` 方法的 ID 映射错误，导致无法正确收集多节点响应
- 📋 缺少连接健康检查，可能在节点故障时影响 Quorum 判断

### 第 4 周里程碑：MVP 完成
**验收标准**：
- [ ] 支持分布式读写操作
- [ ] 版本冲突自动解决
- [ ] 基本的故障容错（节点宕机）
- [ ] 性能达到设计目标（延迟 < 50ms）

### 第 8 周里程碑：生产可用
**验收标准**：
- [ ] 完整的故障恢复机制
- [ ] 数据持久性保证
- [ ] 监控和运维接口

### 第 12 周里程碑：开源就绪
**验收标准**：
- [ ] 完整的文档和示例
- [ ] 性能基准测试
- [ ] 社区贡献指南

## 风险控制和应对策略

### 高风险项目
1. **传输层实现复杂度**
   - 风险：gRPC 连接管理和错误处理复杂
   - 应对：先实现简单版本，逐步完善

2. **版本向量算法正确性**
   - 风险：冲突解决算法实现错误
   - 应对：参考 Dynamo 论文，编写充分测试

3. **性能达不到预期**
   - 风险：网络延迟和序列化开销
   - 应对：早期性能测试，及时优化

### 进度保障措施
- **每周代码审查**：确保实现质量
- **持续集成测试**：及时发现问题
- **里程碑验收**：确保功能完整性

## 预期时间线

- **4 周后**：MVP 版本，基础功能完整
- **8 周后**：Beta 版本，生产可用
- **12 周后**：v1.0 版本，开源发布

这个计划充分利用了现有的 45% 完成度，重点补齐关键缺失功能，预计能在 4 周内达到可用的 MVP 版本。

## 📋 实施进度更新

### 今日完成的工作（架构优化）

虽然偏离了原计划的传输层实现重点，但完成了重要的架构优化：

#### ✅ 已完成的架构优化（影响深远）
- **消除参数转换重复**：移除了 transport 和 internal API 之间的双向类型转换
- **统一类型系统**：全面使用 `internal.MessageType`，消除 `transport.MessageType` 重复定义
- **protobuf 优化**：使用 `google.protobuf.Any` 类型，提升标准化程度
- **代码质量提升**：减少 50+ 行冗余转换代码，提高维护性和性能

#### 📊 传输层当前完成度：62%
**已完成**：
- ✅ Send 同步发送 (100%)
- ✅ AsyncSend 异步发送 (100%)  
- ✅ gRPC 连接复用 (100%)

**部分完成**：
- ⚠️ WaitResponses 响应等待 (30% - 有关键Bug)
- ⚠️ 响应收集机制 (40% - 缺少细粒度控制)

**未完成**：
- ❌ 连接健康检查 (0%)
- ❌ 失败重试机制 (0%)

#### 🎯 下一步行动计划
1. **🔥 紧急修复**：WaitResponses 的 ID 映射问题（阻塞 Quorum 机制）
2. **📅 本周内**：实现连接健康检查
3. **📅 下周**：完善测试覆盖和错误处理

**预期影响**：修复关键Bug后，传输层完成度将达到 85%，足以支持 Quorum 机制正常工作。
