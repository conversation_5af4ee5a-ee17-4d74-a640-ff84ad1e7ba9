package transport

import (
	"context"
	"encoding/json"
	"errors"
	"net"
	"sync"

	"github.com/google/uuid"
	lru "github.com/hashicorp/golang-lru"
	"github.com/panjf2000/ants/v2"

	internal "github.com/suohailong/harmoniakv/internal/api/v1"
	"github.com/suohailong/harmoniakv/internal/config"
	"google.golang.org/grpc"
)

type HandlerFunc func(ctx context.Context, req *internal.MessageRequest) error // 更新 handler 类型

type RawTransporter interface {
	Send(ctx context.Context, target string, req *internal.MessageRequest) (*internal.MessageResponse, error)
	AsyncSend(ctx context.Context, target string, req *internal.MessageRequest) error
	Broadcast(ctx context.Context, targets []string, req *internal.MessageRequest) error
	HandleMessage(ctx context.Context, req *internal.MessageRequest) error
}

type rawTransporter struct {
	pool *ants.Pool
	// ch        chan *internal.InternalResponse       // 使用 protobuf 类型
	lru       *lru.Cache                           // 缓存链接
	udpConn   *net.UDPConn                         // UDP 连接
	mu        sync.Mutex                           // 保护 map
	handlers  map[internal.MessageType]HandlerFunc // 更新为使用 internal.MessageType
	handlerMu sync.RWMutex                         // 保护 handlers
}

func NewRawTransporter() RawTransporter {
	pool, _ := ants.NewPool(10) // 示例大小
	cache, err := lru.NewWithEvict(1000, func(key, value interface{}) {
		if conn, ok := value.(*grpc.ClientConn); ok {
			conn.Close()
		}
	})
	if err != nil {
		panic(err)
	}

	return &rawTransporter{}
}

func (d *rawTransporter) Send(ctx context.Context, req *internal.InternalRequest) (*internal.InternalResponse, error) {
	if req.To == config.NodeAddr() { // 假设 LocalAddr 是 NodeAddr
		return nil, errors.New("local send not supported")
	}
	var conn grpc.ClientConnInterface
	if d.lru.Contains(req.To) {
		con, _ := d.lru.Get(req.To)
		conn = con.(grpc.ClientConnInterface)
	} else {
		conn, err := grpc.DialContext(ctx, req.To, grpc.WithInsecure())
		if err != nil {
			return nil, err
		}
		d.lru.Add(req.To, conn)
	}
	client := internal.NewInternalHarmoniakvClient(conn)

	resp, err := client.InternalCommand(ctx, req)
	if err != nil {
		return nil, err
	}

	// 确保响应包含正确的 MessageId
	if resp != nil && resp.MessageId == "" {
		resp.MessageId = req.MessageId
	}

	return resp, nil
}

func (d *rawTransporter) AsyncSend(ctx context.Context, req *internal.InternalRequest) error {
	if req.MessageId == "" {
		req.MessageId = uuid.New().String() // 自动生成 ID
	}
	return d.pool.Submit(func() {
		resp, err := d.Send(ctx, req)
		// 如果发送失败，创建一个错误响应
		if err != nil {
			resp = &internal.InternalResponse{
				MessageId: req.MessageId,
				Data:      nil, // 可以在这里包装错误信息
			}
		}
		// 确保响应有正确的 MessageId
		if resp != nil && resp.MessageId == "" {
			resp.MessageId = req.MessageId
		}

		d.mu.Lock()
		d.responses[req.MessageId] = resp
		d.mu.Unlock()
		d.ch <- resp // 保留 chan 以兼容
	})
}

// 新增 UDP 广播方法
func (d *rawTransporter) Broadcast(ctx context.Context, targets []string, req *internal.MessageRequest) error {
	if req.MessageId == "" {
		req.MessageId = uuid.New().String()
	}
	data, err := json.Marshal(req) // 使用 JSON 序列化
	if err != nil {
		return err
	}
	for _, target := range targets {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			udpAddr, err := net.ResolveUDPAddr("udp", target)
			if err != nil {
				continue
			}
			_, err = d.udpConn.WriteToUDP(data, udpAddr)
			if err != nil {
				// 日志错误，但继续
			}
		}
	}
	return nil
}

func (d *defaultTransporter) RegisterHandler(t internal.MessageType, fn HandlerFunc) {
	d.handlerMu.Lock()
	d.handlers[t] = fn
	d.handlerMu.Unlock()
}

func (d *defaultTransporter) HandleMessage(ctx context.Context, req *internal.MessageRequest) error {
	if req == nil {
		return errors.New("request cannot be nil")
	}
	d.handlerMu.RLock()
	fn, ok := d.handlers[req.MessageType]
	d.handlerMu.RUnlock()
	if !ok {
		return errors.New("no handler registered for message type")
	}
	// 可添加传输级处理，如日志或认证
	return fn(ctx, req)
}
