# 贡献指南 🤝

感谢您对分布式系统学习实验室的兴趣！我们欢迎所有形式的贡献。

## 🎯 贡献类型

### 代码贡献
- **新算法实现**: 添加新的分布式系统算法
- **性能优化**: 改进现有实现的性能
- **Bug修复**: 修复发现的问题
- **测试增强**: 添加更多测试用例

### 非代码贡献
- **文档改进**: 修正拼写错误、改进说明
- **教程编写**: 添加新的学习教程
- **示例扩展**: 添加更多使用示例
- **翻译**: 将文档翻译成其他语言

## 🚀 开始贡献

### 1. 环境设置

```bash
# 1. Fork 项目到您的GitHub账户
# 2. 克隆您的fork
git clone https://github.com/yourusername/distributed-learning-lab.git
cd distributed-learning-lab

# 3. 添加上游仓库
git remote add upstream https://github.com/original/distributed-learning-lab.git

# 4. 设置开发环境
make dev-setup
```

### 2. 开发流程

#### 创建功能分支
```bash
git checkout main
git pull upstream main
git checkout -b feature/your-feature-name
```

#### 开发规范

**代码规范:**
- 遵循 [Go Code Review Comments](https://github.com/golang/go/wiki/CodeReviewComments)
- 使用 `gofmt` 格式化代码
- 添加必要的注释和文档

**提交信息规范:**
```
type(scope): description

body

footer
```

**类型说明:**
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `test`: 测试相关
- `refactor`: 代码重构
- `perf`: 性能优化

**示例:**
```
feat(raft): add leader election timeout configuration

Add configurable timeout for leader election to handle network partitions
better. Default timeout is 150ms, configurable via environment variable
RAFT_ELECTION_TIMEOUT.

Closes #123
```

#### 测试要求

```bash
# 确保所有测试通过
make test

# 运行代码质量检查
make lint

# 格式化代码
make fmt
```

#### 文档要求

- 每个新功能需要更新README
- 添加或更新相应的教程文档
- 复杂算法需要添加架构决策记录(ADR)

### 3. 提交PR

```bash
# 1. 推送分支
git push origin feature/your-feature-name

# 2. 创建Pull Request
# 在GitHub上创建PR，填写模板

# 3. 响应代码审查
# 根据审查意见进行修改
```

## 🏗️ 项目结构指南

### 添加新算法

```
patterns/
├── consensus/
│   └── your-algorithm/
│       ├── algorithm.go      # 核心实现
│       ├── algorithm_test.go # 测试文件
│       ├── README.md        # 算法说明
│       └── example/         # 使用示例
│           └── main.go
```

### 添加新示例

```
examples/
├── your-example/
│   ├── main.go             # 入口文件
│   ├── README.md           # 使用说明
│   ├── Dockerfile          # 容器化
│   └── docker-compose.yml  # 运行配置
```

## 📝 文档贡献

### 教程文档
- 放在 `docs/tutorials/` 目录
- 使用Markdown格式
- 包含代码示例和运行步骤
- 提供故障排除指南

### 架构决策记录(ADR)
- 放在 `docs/architecture/` 目录
- 文件名格式: `adr-XXX-short-description.md`
- 包含: 背景、决策、后果、替代方案

## 🧪 测试指南

### 单元测试
```go
func TestYourFunction(t *testing.T) {
    tests := []struct {
        name     string
        input    InputType
        expected ExpectedType
    }{
        {"正常情况", input1, expected1},
        {"边界条件", input2, expected2},
        {"错误处理", input3, expected3},
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := YourFunction(tt.input)
            assert.Equal(t, tt.expected, result)
        })
    }
}
```

### 集成测试
- 放在对应模块的测试目录
- 使用Docker环境进行测试
- 包含网络分区和节点故障测试

## 🎨 代码风格

### 命名规范
- **包名**: 小写，简短，有意义
- **函数名**: 驼峰式，动词开头
- **变量名**: 驼峰式，名词，避免缩写

### 代码组织
```go
// 包注释
// Package consensus provides distributed consensus algorithms.
package consensus

// 结构体定义
// Raft represents a Raft consensus node.
type Raft struct {
    mu sync.Mutex // protects following fields
    
    id       string    // node ID
    peers    []string  // peer addresses
    state    NodeState // current state
    
    lastHeartbeat time.Time // last heartbeat time
}

// 函数注释
// Start starts the Raft node and begins participating in consensus.
func (r *Raft) Start() error {
    // implementation
}
```

## 📊 性能基准

### 基准测试
```go
func BenchmarkYourFunction(b *testing.B) {
    for i := 0; i < b.N; i++ {
        YourFunction()
    }
}
```

### 性能报告
- 包含在 `docs/performance/` 目录
- 提供测试环境和结果
- 包含性能优化建议

## 🔧 开发工具

### 推荐工具
- **编辑器**: VS Code + Go扩展
- **调试**: Delve调试器
- **性能**: pprof性能分析
- **容器**: Docker Desktop

### 常用命令
```bash
# 查看帮助
make help

# 运行特定测试
go test -v ./patterns/consensus/raft/...

# 性能分析
go test -bench=. -benchmem ./...

# 生成覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

## 🐛 报告Bug

### Bug报告模板
```markdown
**Bug描述**
清晰简洁的描述问题

**重现步骤**
1. 步骤1
2. 步骤2
3. 步骤3

**期望行为**
描述期望的行为

**实际行为**
描述实际的行为

**环境信息**
- Go版本: 
- 操作系统:
- 复现频率:

**额外信息**
任何其他相关信息
```

## 📞 获取帮助

### 讨论渠道
- **GitHub Discussions**: 一般问题和讨论
- **GitHub Issues**: Bug报告和功能请求
- **Pull Request**: 代码审查相关问题

### 常见问题
查看 [FAQ](docs/faq.md) 获取常见问题解答

## 🏆 贡献者

感谢所有贡献者！

[![Contributors](https://contrib.rocks/image?repo=yourusername/distributed-learning-lab)](https://github.com/yourusername/distributed-learning-lab/graphs/contributors)

## 📄 行为准则

本项目采用 [Contributor Covenant](https://www.contributor-covenant.org/version/2/1/code_of_conduct/) 行为准则。

---

**感谢您的贡献！让我们一起构建更好的分布式系统学习平台。** 🚀