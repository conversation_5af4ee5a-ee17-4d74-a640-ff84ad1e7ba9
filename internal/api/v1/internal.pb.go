// pkg/api/internal/internal.proto

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: internal/api/v1/internal.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MessageType int32

const (
	MessageType_MessageTypeGossip        MessageType = 0
	MessageType_MessageTypeKvCommand     MessageType = 1
	MessageType_MessageTypeNodeJoin      MessageType = 2
	MessageType_MessageTypeNodeLeave     MessageType = 3
	MessageType_MessageTypeAntiEntropy   MessageType = 4
	MessageType_MessageTypeHintedHandoff MessageType = 5
	MessageType_MessageTypeQuorumAck     MessageType = 6
	MessageType_MessageTypeVersionMerge  MessageType = 7
	MessageType_MessageTypeClusterQuery  MessageType = 8
)

// Enum value maps for MessageType.
var (
	MessageType_name = map[int32]string{
		0: "MessageTypeGossip",
		1: "MessageTypeKvCommand",
		2: "MessageTypeNodeJoin",
		3: "MessageTypeNodeLeave",
		4: "MessageTypeAntiEntropy",
		5: "MessageTypeHintedHandoff",
		6: "MessageTypeQuorumAck",
		7: "MessageTypeVersionMerge",
		8: "MessageTypeClusterQuery",
	}
	MessageType_value = map[string]int32{
		"MessageTypeGossip":        0,
		"MessageTypeKvCommand":     1,
		"MessageTypeNodeJoin":      2,
		"MessageTypeNodeLeave":     3,
		"MessageTypeAntiEntropy":   4,
		"MessageTypeHintedHandoff": 5,
		"MessageTypeQuorumAck":     6,
		"MessageTypeVersionMerge":  7,
		"MessageTypeClusterQuery":  8,
	}
)

func (x MessageType) Enum() *MessageType {
	p := new(MessageType)
	*p = x
	return p
}

func (x MessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_api_v1_internal_proto_enumTypes[0].Descriptor()
}

func (MessageType) Type() protoreflect.EnumType {
	return &file_internal_api_v1_internal_proto_enumTypes[0]
}

func (x MessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageType.Descriptor instead.
func (MessageType) EnumDescriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{0}
}

// 应用层错误代码
// 注意：纯传输层错误（如网络连接失败）应该通过 gRPC error 返回，不在此处定义
type ErrorCode int32

const (
	// === 成功和通用错误 ===
	ErrorCode_ERROR_CODE_SUCCESS         ErrorCode = 0
	ErrorCode_ERROR_CODE_UNKNOWN         ErrorCode = 1
	ErrorCode_ERROR_CODE_INVALID_REQUEST ErrorCode = 2
	ErrorCode_ERROR_CODE_TIMEOUT         ErrorCode = 3 // 应用层超时（非网络超时）
	// === Quorum 和分布式协调错误 (2000-2999) ===
	ErrorCode_ERROR_CODE_QUORUM_READ_FAILED      ErrorCode = 2001 // 读取Quorum失败
	ErrorCode_ERROR_CODE_QUORUM_WRITE_FAILED     ErrorCode = 2002 // 写入Quorum失败
	ErrorCode_ERROR_CODE_INSUFFICIENT_REPLICAS   ErrorCode = 2003 // 可用副本不足
	ErrorCode_ERROR_CODE_COORDINATOR_UNAVAILABLE ErrorCode = 2004 // 协调节点不可用
	ErrorCode_ERROR_CODE_REPLICA_NOT_RESPONDING  ErrorCode = 2005 // 副本节点无响应（应用层检测）
	// === 版本冲突和一致性错误 (3000-3999) ===
	ErrorCode_ERROR_CODE_VERSION_CONFLICT        ErrorCode = 3001 // 版本向量冲突
	ErrorCode_ERROR_CODE_MERGE_FAILED            ErrorCode = 3002 // 版本合并失败
	ErrorCode_ERROR_CODE_ANTI_ENTROPY_FAILED     ErrorCode = 3003 // 反熵同步失败
	ErrorCode_ERROR_CODE_STALE_READ              ErrorCode = 3004 // 读取到过期数据
	ErrorCode_ERROR_CODE_CONCURRENT_MODIFICATION ErrorCode = 3005 // 并发修改冲突
	// === 集群管理和成员错误 (4000-4999) ===
	ErrorCode_ERROR_CODE_NODE_JOIN_FAILED           ErrorCode = 4001 // 节点加入失败
	ErrorCode_ERROR_CODE_NODE_LEAVE_FAILED          ErrorCode = 4002 // 节点离开失败
	ErrorCode_ERROR_CODE_GOSSIP_FAILED              ErrorCode = 4003 // Gossip协议失败
	ErrorCode_ERROR_CODE_CLUSTER_STATE_INCONSISTENT ErrorCode = 4004 // 集群状态不一致
	ErrorCode_ERROR_CODE_MEMBERSHIP_CHANGE_REJECTED ErrorCode = 4005 // 成员变更被拒绝
	ErrorCode_ERROR_CODE_SPLIT_BRAIN_DETECTED       ErrorCode = 4006 // 检测到脑裂
	// === 存储和数据错误 (5000-5999) ===
	ErrorCode_ERROR_CODE_KEY_NOT_FOUND         ErrorCode = 5001 // 键不存在
	ErrorCode_ERROR_CODE_STORAGE_FULL          ErrorCode = 5002 // 存储空间不足
	ErrorCode_ERROR_CODE_STORAGE_CORRUPTION    ErrorCode = 5003 // 数据损坏
	ErrorCode_ERROR_CODE_HINTED_HANDOFF_FAILED ErrorCode = 5004 // 暗示移交失败
	ErrorCode_ERROR_CODE_INVALID_KEY_FORMAT    ErrorCode = 5005 // 键格式无效
	ErrorCode_ERROR_CODE_VALUE_TOO_LARGE       ErrorCode = 5006 // 值过大
	// === 认证和授权错误 (6000-6999) ===
	ErrorCode_ERROR_CODE_UNAUTHORIZED        ErrorCode = 6001 // 未授权
	ErrorCode_ERROR_CODE_TOKEN_EXPIRED       ErrorCode = 6002 // Token过期
	ErrorCode_ERROR_CODE_PERMISSION_DENIED   ErrorCode = 6003 // 权限不足
	ErrorCode_ERROR_CODE_INVALID_CREDENTIALS ErrorCode = 6004 // 凭据无效
)

// Enum value maps for ErrorCode.
var (
	ErrorCode_name = map[int32]string{
		0:    "ERROR_CODE_SUCCESS",
		1:    "ERROR_CODE_UNKNOWN",
		2:    "ERROR_CODE_INVALID_REQUEST",
		3:    "ERROR_CODE_TIMEOUT",
		2001: "ERROR_CODE_QUORUM_READ_FAILED",
		2002: "ERROR_CODE_QUORUM_WRITE_FAILED",
		2003: "ERROR_CODE_INSUFFICIENT_REPLICAS",
		2004: "ERROR_CODE_COORDINATOR_UNAVAILABLE",
		2005: "ERROR_CODE_REPLICA_NOT_RESPONDING",
		3001: "ERROR_CODE_VERSION_CONFLICT",
		3002: "ERROR_CODE_MERGE_FAILED",
		3003: "ERROR_CODE_ANTI_ENTROPY_FAILED",
		3004: "ERROR_CODE_STALE_READ",
		3005: "ERROR_CODE_CONCURRENT_MODIFICATION",
		4001: "ERROR_CODE_NODE_JOIN_FAILED",
		4002: "ERROR_CODE_NODE_LEAVE_FAILED",
		4003: "ERROR_CODE_GOSSIP_FAILED",
		4004: "ERROR_CODE_CLUSTER_STATE_INCONSISTENT",
		4005: "ERROR_CODE_MEMBERSHIP_CHANGE_REJECTED",
		4006: "ERROR_CODE_SPLIT_BRAIN_DETECTED",
		5001: "ERROR_CODE_KEY_NOT_FOUND",
		5002: "ERROR_CODE_STORAGE_FULL",
		5003: "ERROR_CODE_STORAGE_CORRUPTION",
		5004: "ERROR_CODE_HINTED_HANDOFF_FAILED",
		5005: "ERROR_CODE_INVALID_KEY_FORMAT",
		5006: "ERROR_CODE_VALUE_TOO_LARGE",
		6001: "ERROR_CODE_UNAUTHORIZED",
		6002: "ERROR_CODE_TOKEN_EXPIRED",
		6003: "ERROR_CODE_PERMISSION_DENIED",
		6004: "ERROR_CODE_INVALID_CREDENTIALS",
	}
	ErrorCode_value = map[string]int32{
		"ERROR_CODE_SUCCESS":                    0,
		"ERROR_CODE_UNKNOWN":                    1,
		"ERROR_CODE_INVALID_REQUEST":            2,
		"ERROR_CODE_TIMEOUT":                    3,
		"ERROR_CODE_QUORUM_READ_FAILED":         2001,
		"ERROR_CODE_QUORUM_WRITE_FAILED":        2002,
		"ERROR_CODE_INSUFFICIENT_REPLICAS":      2003,
		"ERROR_CODE_COORDINATOR_UNAVAILABLE":    2004,
		"ERROR_CODE_REPLICA_NOT_RESPONDING":     2005,
		"ERROR_CODE_VERSION_CONFLICT":           3001,
		"ERROR_CODE_MERGE_FAILED":               3002,
		"ERROR_CODE_ANTI_ENTROPY_FAILED":        3003,
		"ERROR_CODE_STALE_READ":                 3004,
		"ERROR_CODE_CONCURRENT_MODIFICATION":    3005,
		"ERROR_CODE_NODE_JOIN_FAILED":           4001,
		"ERROR_CODE_NODE_LEAVE_FAILED":          4002,
		"ERROR_CODE_GOSSIP_FAILED":              4003,
		"ERROR_CODE_CLUSTER_STATE_INCONSISTENT": 4004,
		"ERROR_CODE_MEMBERSHIP_CHANGE_REJECTED": 4005,
		"ERROR_CODE_SPLIT_BRAIN_DETECTED":       4006,
		"ERROR_CODE_KEY_NOT_FOUND":              5001,
		"ERROR_CODE_STORAGE_FULL":               5002,
		"ERROR_CODE_STORAGE_CORRUPTION":         5003,
		"ERROR_CODE_HINTED_HANDOFF_FAILED":      5004,
		"ERROR_CODE_INVALID_KEY_FORMAT":         5005,
		"ERROR_CODE_VALUE_TOO_LARGE":            5006,
		"ERROR_CODE_UNAUTHORIZED":               6001,
		"ERROR_CODE_TOKEN_EXPIRED":              6002,
		"ERROR_CODE_PERMISSION_DENIED":          6003,
		"ERROR_CODE_INVALID_CREDENTIALS":        6004,
	}
)

func (x ErrorCode) Enum() *ErrorCode {
	p := new(ErrorCode)
	*p = x
	return p
}

func (x ErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_api_v1_internal_proto_enumTypes[1].Descriptor()
}

func (ErrorCode) Type() protoreflect.EnumType {
	return &file_internal_api_v1_internal_proto_enumTypes[1]
}

func (x ErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorCode.Descriptor instead.
func (ErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{1}
}

type Error struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Code              int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`                                                                                // 使用 ErrorCode 枚举值
	Message           string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`                                                                           // 人类可读的错误消息
	Details           map[string]string      `protobuf:"bytes,3,rep,name=details,proto3" json:"details,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // 结构化错误详情
	Category          string                 `protobuf:"bytes,4,opt,name=category,proto3" json:"category,omitempty"`                                                                         // 错误分类：quorum, consistency, cluster, storage, auth
	Retryable         bool                   `protobuf:"varint,5,opt,name=retryable,proto3" json:"retryable,omitempty"`                                                                      // 是否可重试
	RetryAfterSeconds int32                  `protobuf:"varint,6,opt,name=retry_after_seconds,json=retryAfterSeconds,proto3" json:"retry_after_seconds,omitempty"`                           // 建议重试间隔（秒）
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Error) Reset() {
	*x = Error{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Error) ProtoMessage() {}

func (x *Error) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Error.ProtoReflect.Descriptor instead.
func (*Error) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{0}
}

func (x *Error) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Error) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Error) GetDetails() map[string]string {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *Error) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *Error) GetRetryable() bool {
	if x != nil {
		return x.Retryable
	}
	return false
}

func (x *Error) GetRetryAfterSeconds() int32 {
	if x != nil {
		return x.RetryAfterSeconds
	}
	return 0
}

type KvCommand struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KvCommand) Reset() {
	*x = KvCommand{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KvCommand) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KvCommand) ProtoMessage() {}

func (x *KvCommand) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KvCommand.ProtoReflect.Descriptor instead.
func (*KvCommand) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{1}
}

type GossipMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GossipMessage) Reset() {
	*x = GossipMessage{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GossipMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GossipMessage) ProtoMessage() {}

func (x *GossipMessage) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GossipMessage.ProtoReflect.Descriptor instead.
func (*GossipMessage) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{2}
}

type QuorumRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuorumRequest) Reset() {
	*x = QuorumRequest{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuorumRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuorumRequest) ProtoMessage() {}

func (x *QuorumRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuorumRequest.ProtoReflect.Descriptor instead.
func (*QuorumRequest) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{3}
}

type NodeJoinRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeJoinRequest) Reset() {
	*x = NodeJoinRequest{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeJoinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeJoinRequest) ProtoMessage() {}

func (x *NodeJoinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeJoinRequest.ProtoReflect.Descriptor instead.
func (*NodeJoinRequest) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{4}
}

type NodeLeaveRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeLeaveRequest) Reset() {
	*x = NodeLeaveRequest{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeLeaveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeLeaveRequest) ProtoMessage() {}

func (x *NodeLeaveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeLeaveRequest.ProtoReflect.Descriptor instead.
func (*NodeLeaveRequest) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{5}
}

type AntiEntropyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AntiEntropyRequest) Reset() {
	*x = AntiEntropyRequest{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AntiEntropyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AntiEntropyRequest) ProtoMessage() {}

func (x *AntiEntropyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AntiEntropyRequest.ProtoReflect.Descriptor instead.
func (*AntiEntropyRequest) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{6}
}

type HintedHandoffRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HintedHandoffRequest) Reset() {
	*x = HintedHandoffRequest{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HintedHandoffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HintedHandoffRequest) ProtoMessage() {}

func (x *HintedHandoffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HintedHandoffRequest.ProtoReflect.Descriptor instead.
func (*HintedHandoffRequest) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{7}
}

type QuorumAckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuorumAckRequest) Reset() {
	*x = QuorumAckRequest{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuorumAckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuorumAckRequest) ProtoMessage() {}

func (x *QuorumAckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuorumAckRequest.ProtoReflect.Descriptor instead.
func (*QuorumAckRequest) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{8}
}

type VersionMergeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VersionMergeRequest) Reset() {
	*x = VersionMergeRequest{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VersionMergeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionMergeRequest) ProtoMessage() {}

func (x *VersionMergeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionMergeRequest.ProtoReflect.Descriptor instead.
func (*VersionMergeRequest) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{9}
}

type ClusterQueryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClusterQueryRequest) Reset() {
	*x = ClusterQueryRequest{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClusterQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterQueryRequest) ProtoMessage() {}

func (x *ClusterQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterQueryRequest.ProtoReflect.Descriptor instead.
func (*ClusterQueryRequest) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{10}
}

type MessageRequest struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	Token       string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`                                                  // 用于认证
	RequestId   string                 `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`                         // 对应 Message.ID
	MessageType MessageType            `protobuf:"varint,3,opt,name=message_type,json=messageType,proto3,enum=MessageType" json:"message_type,omitempty"` // 对应 Message.MessageType
	From        string                 `protobuf:"bytes,4,opt,name=from,proto3" json:"from,omitempty"`                                                    // 对应 Message.From
	To          string                 `protobuf:"bytes,5,opt,name=to,proto3" json:"to,omitempty"`                                                        // 对应 Message.To
	// Types that are valid to be assigned to Request:
	//
	//	*MessageRequest_KvCommand
	//	*MessageRequest_GossipMessage
	//	*MessageRequest_QuorumRequest
	//	*MessageRequest_NodeJoinRequest
	//	*MessageRequest_NodeLeaveRequest
	//	*MessageRequest_AntiEntropyRequest
	//	*MessageRequest_HintedHandoffRequest
	//	*MessageRequest_QuorumAckRequest
	//	*MessageRequest_VersionMergeRequest
	//	*MessageRequest_ClusterQueryRequest
	Request       isMessageRequest_Request `protobuf_oneof:"request"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageRequest) Reset() {
	*x = MessageRequest{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageRequest) ProtoMessage() {}

func (x *MessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageRequest.ProtoReflect.Descriptor instead.
func (*MessageRequest) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{11}
}

func (x *MessageRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *MessageRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *MessageRequest) GetMessageType() MessageType {
	if x != nil {
		return x.MessageType
	}
	return MessageType_MessageTypeGossip
}

func (x *MessageRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *MessageRequest) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *MessageRequest) GetRequest() isMessageRequest_Request {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *MessageRequest) GetKvCommand() *KvCommand {
	if x != nil {
		if x, ok := x.Request.(*MessageRequest_KvCommand); ok {
			return x.KvCommand
		}
	}
	return nil
}

func (x *MessageRequest) GetGossipMessage() *GossipMessage {
	if x != nil {
		if x, ok := x.Request.(*MessageRequest_GossipMessage); ok {
			return x.GossipMessage
		}
	}
	return nil
}

func (x *MessageRequest) GetQuorumRequest() *QuorumRequest {
	if x != nil {
		if x, ok := x.Request.(*MessageRequest_QuorumRequest); ok {
			return x.QuorumRequest
		}
	}
	return nil
}

func (x *MessageRequest) GetNodeJoinRequest() *NodeJoinRequest {
	if x != nil {
		if x, ok := x.Request.(*MessageRequest_NodeJoinRequest); ok {
			return x.NodeJoinRequest
		}
	}
	return nil
}

func (x *MessageRequest) GetNodeLeaveRequest() *NodeLeaveRequest {
	if x != nil {
		if x, ok := x.Request.(*MessageRequest_NodeLeaveRequest); ok {
			return x.NodeLeaveRequest
		}
	}
	return nil
}

func (x *MessageRequest) GetAntiEntropyRequest() *AntiEntropyRequest {
	if x != nil {
		if x, ok := x.Request.(*MessageRequest_AntiEntropyRequest); ok {
			return x.AntiEntropyRequest
		}
	}
	return nil
}

func (x *MessageRequest) GetHintedHandoffRequest() *HintedHandoffRequest {
	if x != nil {
		if x, ok := x.Request.(*MessageRequest_HintedHandoffRequest); ok {
			return x.HintedHandoffRequest
		}
	}
	return nil
}

func (x *MessageRequest) GetQuorumAckRequest() *QuorumAckRequest {
	if x != nil {
		if x, ok := x.Request.(*MessageRequest_QuorumAckRequest); ok {
			return x.QuorumAckRequest
		}
	}
	return nil
}

func (x *MessageRequest) GetVersionMergeRequest() *VersionMergeRequest {
	if x != nil {
		if x, ok := x.Request.(*MessageRequest_VersionMergeRequest); ok {
			return x.VersionMergeRequest
		}
	}
	return nil
}

func (x *MessageRequest) GetClusterQueryRequest() *ClusterQueryRequest {
	if x != nil {
		if x, ok := x.Request.(*MessageRequest_ClusterQueryRequest); ok {
			return x.ClusterQueryRequest
		}
	}
	return nil
}

type isMessageRequest_Request interface {
	isMessageRequest_Request()
}

type MessageRequest_KvCommand struct {
	KvCommand *KvCommand `protobuf:"bytes,6,opt,name=kv_command,json=kvCommand,proto3,oneof"`
}

type MessageRequest_GossipMessage struct {
	GossipMessage *GossipMessage `protobuf:"bytes,7,opt,name=gossip_message,json=gossipMessage,proto3,oneof"`
}

type MessageRequest_QuorumRequest struct {
	QuorumRequest *QuorumRequest `protobuf:"bytes,8,opt,name=quorum_request,json=quorumRequest,proto3,oneof"`
}

type MessageRequest_NodeJoinRequest struct {
	NodeJoinRequest *NodeJoinRequest `protobuf:"bytes,9,opt,name=node_join_request,json=nodeJoinRequest,proto3,oneof"`
}

type MessageRequest_NodeLeaveRequest struct {
	NodeLeaveRequest *NodeLeaveRequest `protobuf:"bytes,10,opt,name=node_leave_request,json=nodeLeaveRequest,proto3,oneof"`
}

type MessageRequest_AntiEntropyRequest struct {
	AntiEntropyRequest *AntiEntropyRequest `protobuf:"bytes,11,opt,name=anti_entropy_request,json=antiEntropyRequest,proto3,oneof"`
}

type MessageRequest_HintedHandoffRequest struct {
	HintedHandoffRequest *HintedHandoffRequest `protobuf:"bytes,12,opt,name=hinted_handoff_request,json=hintedHandoffRequest,proto3,oneof"`
}

type MessageRequest_QuorumAckRequest struct {
	QuorumAckRequest *QuorumAckRequest `protobuf:"bytes,13,opt,name=quorum_ack_request,json=quorumAckRequest,proto3,oneof"`
}

type MessageRequest_VersionMergeRequest struct {
	VersionMergeRequest *VersionMergeRequest `protobuf:"bytes,14,opt,name=version_merge_request,json=versionMergeRequest,proto3,oneof"`
}

type MessageRequest_ClusterQueryRequest struct {
	ClusterQueryRequest *ClusterQueryRequest `protobuf:"bytes,15,opt,name=cluster_query_request,json=clusterQueryRequest,proto3,oneof"`
}

func (*MessageRequest_KvCommand) isMessageRequest_Request() {}

func (*MessageRequest_GossipMessage) isMessageRequest_Request() {}

func (*MessageRequest_QuorumRequest) isMessageRequest_Request() {}

func (*MessageRequest_NodeJoinRequest) isMessageRequest_Request() {}

func (*MessageRequest_NodeLeaveRequest) isMessageRequest_Request() {}

func (*MessageRequest_AntiEntropyRequest) isMessageRequest_Request() {}

func (*MessageRequest_HintedHandoffRequest) isMessageRequest_Request() {}

func (*MessageRequest_QuorumAckRequest) isMessageRequest_Request() {}

func (*MessageRequest_VersionMergeRequest) isMessageRequest_Request() {}

func (*MessageRequest_ClusterQueryRequest) isMessageRequest_Request() {}

type KvCommandResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KvCommandResponse) Reset() {
	*x = KvCommandResponse{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KvCommandResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KvCommandResponse) ProtoMessage() {}

func (x *KvCommandResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KvCommandResponse.ProtoReflect.Descriptor instead.
func (*KvCommandResponse) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{12}
}

type GossipResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GossipResponse) Reset() {
	*x = GossipResponse{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GossipResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GossipResponse) ProtoMessage() {}

func (x *GossipResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GossipResponse.ProtoReflect.Descriptor instead.
func (*GossipResponse) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{13}
}

type AntiEntropyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AntiEntropyResponse) Reset() {
	*x = AntiEntropyResponse{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AntiEntropyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AntiEntropyResponse) ProtoMessage() {}

func (x *AntiEntropyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AntiEntropyResponse.ProtoReflect.Descriptor instead.
func (*AntiEntropyResponse) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{14}
}

type HintedHandoffResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HintedHandoffResponse) Reset() {
	*x = HintedHandoffResponse{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HintedHandoffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HintedHandoffResponse) ProtoMessage() {}

func (x *HintedHandoffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HintedHandoffResponse.ProtoReflect.Descriptor instead.
func (*HintedHandoffResponse) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{15}
}

type QuorumResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuorumResponse) Reset() {
	*x = QuorumResponse{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuorumResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuorumResponse) ProtoMessage() {}

func (x *QuorumResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuorumResponse.ProtoReflect.Descriptor instead.
func (*QuorumResponse) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{16}
}

type NodeJoinResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeJoinResponse) Reset() {
	*x = NodeJoinResponse{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeJoinResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeJoinResponse) ProtoMessage() {}

func (x *NodeJoinResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeJoinResponse.ProtoReflect.Descriptor instead.
func (*NodeJoinResponse) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{17}
}

type NodeLeaveResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeLeaveResponse) Reset() {
	*x = NodeLeaveResponse{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeLeaveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeLeaveResponse) ProtoMessage() {}

func (x *NodeLeaveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeLeaveResponse.ProtoReflect.Descriptor instead.
func (*NodeLeaveResponse) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{18}
}

type QuorumAckResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuorumAckResponse) Reset() {
	*x = QuorumAckResponse{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuorumAckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuorumAckResponse) ProtoMessage() {}

func (x *QuorumAckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuorumAckResponse.ProtoReflect.Descriptor instead.
func (*QuorumAckResponse) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{19}
}

type VersionMergeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VersionMergeResponse) Reset() {
	*x = VersionMergeResponse{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VersionMergeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionMergeResponse) ProtoMessage() {}

func (x *VersionMergeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionMergeResponse.ProtoReflect.Descriptor instead.
func (*VersionMergeResponse) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{20}
}

type ClusterQueryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClusterQueryResponse) Reset() {
	*x = ClusterQueryResponse{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClusterQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterQueryResponse) ProtoMessage() {}

func (x *ClusterQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterQueryResponse.ProtoReflect.Descriptor instead.
func (*ClusterQueryResponse) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{21}
}

type MessageResponse struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	RequestId string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"` // 对应 Message.ID
	Success   bool                   `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
	Error     *Error                 `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	// Types that are valid to be assigned to Response:
	//
	//	*MessageResponse_KvResponse
	//	*MessageResponse_GossipResponse
	//	*MessageResponse_NodeJoinResponse
	//	*MessageResponse_NodeLeaveResponse
	//	*MessageResponse_AntiEntropyResponse
	//	*MessageResponse_HintedHandoffResponse
	//	*MessageResponse_QuorumAckResponse
	//	*MessageResponse_VersionMergeResponse
	//	*MessageResponse_ClusterQueryResponse
	Response      isMessageResponse_Response `protobuf_oneof:"response"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageResponse) Reset() {
	*x = MessageResponse{}
	mi := &file_internal_api_v1_internal_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageResponse) ProtoMessage() {}

func (x *MessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_api_v1_internal_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageResponse.ProtoReflect.Descriptor instead.
func (*MessageResponse) Descriptor() ([]byte, []int) {
	return file_internal_api_v1_internal_proto_rawDescGZIP(), []int{22}
}

func (x *MessageResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *MessageResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *MessageResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MessageResponse) GetResponse() isMessageResponse_Response {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *MessageResponse) GetKvResponse() *KvCommandResponse {
	if x != nil {
		if x, ok := x.Response.(*MessageResponse_KvResponse); ok {
			return x.KvResponse
		}
	}
	return nil
}

func (x *MessageResponse) GetGossipResponse() *GossipResponse {
	if x != nil {
		if x, ok := x.Response.(*MessageResponse_GossipResponse); ok {
			return x.GossipResponse
		}
	}
	return nil
}

func (x *MessageResponse) GetNodeJoinResponse() *NodeJoinResponse {
	if x != nil {
		if x, ok := x.Response.(*MessageResponse_NodeJoinResponse); ok {
			return x.NodeJoinResponse
		}
	}
	return nil
}

func (x *MessageResponse) GetNodeLeaveResponse() *NodeLeaveResponse {
	if x != nil {
		if x, ok := x.Response.(*MessageResponse_NodeLeaveResponse); ok {
			return x.NodeLeaveResponse
		}
	}
	return nil
}

func (x *MessageResponse) GetAntiEntropyResponse() *AntiEntropyResponse {
	if x != nil {
		if x, ok := x.Response.(*MessageResponse_AntiEntropyResponse); ok {
			return x.AntiEntropyResponse
		}
	}
	return nil
}

func (x *MessageResponse) GetHintedHandoffResponse() *HintedHandoffResponse {
	if x != nil {
		if x, ok := x.Response.(*MessageResponse_HintedHandoffResponse); ok {
			return x.HintedHandoffResponse
		}
	}
	return nil
}

func (x *MessageResponse) GetQuorumAckResponse() *QuorumAckResponse {
	if x != nil {
		if x, ok := x.Response.(*MessageResponse_QuorumAckResponse); ok {
			return x.QuorumAckResponse
		}
	}
	return nil
}

func (x *MessageResponse) GetVersionMergeResponse() *VersionMergeResponse {
	if x != nil {
		if x, ok := x.Response.(*MessageResponse_VersionMergeResponse); ok {
			return x.VersionMergeResponse
		}
	}
	return nil
}

func (x *MessageResponse) GetClusterQueryResponse() *ClusterQueryResponse {
	if x != nil {
		if x, ok := x.Response.(*MessageResponse_ClusterQueryResponse); ok {
			return x.ClusterQueryResponse
		}
	}
	return nil
}

type isMessageResponse_Response interface {
	isMessageResponse_Response()
}

type MessageResponse_KvResponse struct {
	KvResponse *KvCommandResponse `protobuf:"bytes,10,opt,name=kv_response,json=kvResponse,proto3,oneof"`
}

type MessageResponse_GossipResponse struct {
	GossipResponse *GossipResponse `protobuf:"bytes,11,opt,name=gossip_response,json=gossipResponse,proto3,oneof"`
}

type MessageResponse_NodeJoinResponse struct {
	NodeJoinResponse *NodeJoinResponse `protobuf:"bytes,12,opt,name=node_join_response,json=nodeJoinResponse,proto3,oneof"`
}

type MessageResponse_NodeLeaveResponse struct {
	NodeLeaveResponse *NodeLeaveResponse `protobuf:"bytes,13,opt,name=node_leave_response,json=nodeLeaveResponse,proto3,oneof"`
}

type MessageResponse_AntiEntropyResponse struct {
	AntiEntropyResponse *AntiEntropyResponse `protobuf:"bytes,14,opt,name=anti_entropy_response,json=antiEntropyResponse,proto3,oneof"`
}

type MessageResponse_HintedHandoffResponse struct {
	HintedHandoffResponse *HintedHandoffResponse `protobuf:"bytes,15,opt,name=hinted_handoff_response,json=hintedHandoffResponse,proto3,oneof"`
}

type MessageResponse_QuorumAckResponse struct {
	QuorumAckResponse *QuorumAckResponse `protobuf:"bytes,16,opt,name=quorum_ack_response,json=quorumAckResponse,proto3,oneof"`
}

type MessageResponse_VersionMergeResponse struct {
	VersionMergeResponse *VersionMergeResponse `protobuf:"bytes,17,opt,name=version_merge_response,json=versionMergeResponse,proto3,oneof"`
}

type MessageResponse_ClusterQueryResponse struct {
	ClusterQueryResponse *ClusterQueryResponse `protobuf:"bytes,18,opt,name=cluster_query_response,json=clusterQueryResponse,proto3,oneof"`
}

func (*MessageResponse_KvResponse) isMessageResponse_Response() {}

func (*MessageResponse_GossipResponse) isMessageResponse_Response() {}

func (*MessageResponse_NodeJoinResponse) isMessageResponse_Response() {}

func (*MessageResponse_NodeLeaveResponse) isMessageResponse_Response() {}

func (*MessageResponse_AntiEntropyResponse) isMessageResponse_Response() {}

func (*MessageResponse_HintedHandoffResponse) isMessageResponse_Response() {}

func (*MessageResponse_QuorumAckResponse) isMessageResponse_Response() {}

func (*MessageResponse_VersionMergeResponse) isMessageResponse_Response() {}

func (*MessageResponse_ClusterQueryResponse) isMessageResponse_Response() {}

var File_internal_api_v1_internal_proto protoreflect.FileDescriptor

const file_internal_api_v1_internal_proto_rawDesc = "" +
	"\n" +
	"\x1einternal/api/v1/internal.proto\"\x8a\x02\n" +
	"\x05Error\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12-\n" +
	"\adetails\x18\x03 \x03(\v2\x13.Error.DetailsEntryR\adetails\x12\x1a\n" +
	"\bcategory\x18\x04 \x01(\tR\bcategory\x12\x1c\n" +
	"\tretryable\x18\x05 \x01(\bR\tretryable\x12.\n" +
	"\x13retry_after_seconds\x18\x06 \x01(\x05R\x11retryAfterSeconds\x1a:\n" +
	"\fDetailsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\v\n" +
	"\tKvCommand\"\x0f\n" +
	"\rGossipMessage\"\x0f\n" +
	"\rQuorumRequest\"\x11\n" +
	"\x0fNodeJoinRequest\"\x12\n" +
	"\x10NodeLeaveRequest\"\x14\n" +
	"\x12AntiEntropyRequest\"\x16\n" +
	"\x14HintedHandoffRequest\"\x12\n" +
	"\x10QuorumAckRequest\"\x15\n" +
	"\x13VersionMergeRequest\"\x15\n" +
	"\x13ClusterQueryRequest\"\xba\x06\n" +
	"\x0eMessageRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x1d\n" +
	"\n" +
	"request_id\x18\x02 \x01(\tR\trequestId\x12/\n" +
	"\fmessage_type\x18\x03 \x01(\x0e2\f.MessageTypeR\vmessageType\x12\x12\n" +
	"\x04from\x18\x04 \x01(\tR\x04from\x12\x0e\n" +
	"\x02to\x18\x05 \x01(\tR\x02to\x12+\n" +
	"\n" +
	"kv_command\x18\x06 \x01(\v2\n" +
	".KvCommandH\x00R\tkvCommand\x127\n" +
	"\x0egossip_message\x18\a \x01(\v2\x0e.GossipMessageH\x00R\rgossipMessage\x127\n" +
	"\x0equorum_request\x18\b \x01(\v2\x0e.QuorumRequestH\x00R\rquorumRequest\x12>\n" +
	"\x11node_join_request\x18\t \x01(\v2\x10.NodeJoinRequestH\x00R\x0fnodeJoinRequest\x12A\n" +
	"\x12node_leave_request\x18\n" +
	" \x01(\v2\x11.NodeLeaveRequestH\x00R\x10nodeLeaveRequest\x12G\n" +
	"\x14anti_entropy_request\x18\v \x01(\v2\x13.AntiEntropyRequestH\x00R\x12antiEntropyRequest\x12M\n" +
	"\x16hinted_handoff_request\x18\f \x01(\v2\x15.HintedHandoffRequestH\x00R\x14hintedHandoffRequest\x12A\n" +
	"\x12quorum_ack_request\x18\r \x01(\v2\x11.QuorumAckRequestH\x00R\x10quorumAckRequest\x12J\n" +
	"\x15version_merge_request\x18\x0e \x01(\v2\x14.VersionMergeRequestH\x00R\x13versionMergeRequest\x12J\n" +
	"\x15cluster_query_request\x18\x0f \x01(\v2\x14.ClusterQueryRequestH\x00R\x13clusterQueryRequestB\t\n" +
	"\arequest\"\x13\n" +
	"\x11KvCommandResponse\"\x10\n" +
	"\x0eGossipResponse\"\x15\n" +
	"\x13AntiEntropyResponse\"\x17\n" +
	"\x15HintedHandoffResponse\"\x10\n" +
	"\x0eQuorumResponse\"\x12\n" +
	"\x10NodeJoinResponse\"\x13\n" +
	"\x11NodeLeaveResponse\"\x13\n" +
	"\x11QuorumAckResponse\"\x16\n" +
	"\x14VersionMergeResponse\"\x16\n" +
	"\x14ClusterQueryResponse\"\xf2\x05\n" +
	"\x0fMessageResponse\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x18\n" +
	"\asuccess\x18\x03 \x01(\bR\asuccess\x12\x1c\n" +
	"\x05error\x18\x04 \x01(\v2\x06.ErrorR\x05error\x125\n" +
	"\vkv_response\x18\n" +
	" \x01(\v2\x12.KvCommandResponseH\x00R\n" +
	"kvResponse\x12:\n" +
	"\x0fgossip_response\x18\v \x01(\v2\x0f.GossipResponseH\x00R\x0egossipResponse\x12A\n" +
	"\x12node_join_response\x18\f \x01(\v2\x11.NodeJoinResponseH\x00R\x10nodeJoinResponse\x12D\n" +
	"\x13node_leave_response\x18\r \x01(\v2\x12.NodeLeaveResponseH\x00R\x11nodeLeaveResponse\x12J\n" +
	"\x15anti_entropy_response\x18\x0e \x01(\v2\x14.AntiEntropyResponseH\x00R\x13antiEntropyResponse\x12P\n" +
	"\x17hinted_handoff_response\x18\x0f \x01(\v2\x16.HintedHandoffResponseH\x00R\x15hintedHandoffResponse\x12D\n" +
	"\x13quorum_ack_response\x18\x10 \x01(\v2\x12.QuorumAckResponseH\x00R\x11quorumAckResponse\x12M\n" +
	"\x16version_merge_response\x18\x11 \x01(\v2\x15.VersionMergeResponseH\x00R\x14versionMergeResponse\x12M\n" +
	"\x16cluster_query_response\x18\x12 \x01(\v2\x15.ClusterQueryResponseH\x00R\x14clusterQueryResponseB\n" +
	"\n" +
	"\bresponse*\xff\x01\n" +
	"\vMessageType\x12\x15\n" +
	"\x11MessageTypeGossip\x10\x00\x12\x18\n" +
	"\x14MessageTypeKvCommand\x10\x01\x12\x17\n" +
	"\x13MessageTypeNodeJoin\x10\x02\x12\x18\n" +
	"\x14MessageTypeNodeLeave\x10\x03\x12\x1a\n" +
	"\x16MessageTypeAntiEntropy\x10\x04\x12\x1c\n" +
	"\x18MessageTypeHintedHandoff\x10\x05\x12\x18\n" +
	"\x14MessageTypeQuorumAck\x10\x06\x12\x1b\n" +
	"\x17MessageTypeVersionMerge\x10\a\x12\x1b\n" +
	"\x17MessageTypeClusterQuery\x10\b*\x92\b\n" +
	"\tErrorCode\x12\x16\n" +
	"\x12ERROR_CODE_SUCCESS\x10\x00\x12\x16\n" +
	"\x12ERROR_CODE_UNKNOWN\x10\x01\x12\x1e\n" +
	"\x1aERROR_CODE_INVALID_REQUEST\x10\x02\x12\x16\n" +
	"\x12ERROR_CODE_TIMEOUT\x10\x03\x12\"\n" +
	"\x1dERROR_CODE_QUORUM_READ_FAILED\x10\xd1\x0f\x12#\n" +
	"\x1eERROR_CODE_QUORUM_WRITE_FAILED\x10\xd2\x0f\x12%\n" +
	" ERROR_CODE_INSUFFICIENT_REPLICAS\x10\xd3\x0f\x12'\n" +
	"\"ERROR_CODE_COORDINATOR_UNAVAILABLE\x10\xd4\x0f\x12&\n" +
	"!ERROR_CODE_REPLICA_NOT_RESPONDING\x10\xd5\x0f\x12 \n" +
	"\x1bERROR_CODE_VERSION_CONFLICT\x10\xb9\x17\x12\x1c\n" +
	"\x17ERROR_CODE_MERGE_FAILED\x10\xba\x17\x12#\n" +
	"\x1eERROR_CODE_ANTI_ENTROPY_FAILED\x10\xbb\x17\x12\x1a\n" +
	"\x15ERROR_CODE_STALE_READ\x10\xbc\x17\x12'\n" +
	"\"ERROR_CODE_CONCURRENT_MODIFICATION\x10\xbd\x17\x12 \n" +
	"\x1bERROR_CODE_NODE_JOIN_FAILED\x10\xa1\x1f\x12!\n" +
	"\x1cERROR_CODE_NODE_LEAVE_FAILED\x10\xa2\x1f\x12\x1d\n" +
	"\x18ERROR_CODE_GOSSIP_FAILED\x10\xa3\x1f\x12*\n" +
	"%ERROR_CODE_CLUSTER_STATE_INCONSISTENT\x10\xa4\x1f\x12*\n" +
	"%ERROR_CODE_MEMBERSHIP_CHANGE_REJECTED\x10\xa5\x1f\x12$\n" +
	"\x1fERROR_CODE_SPLIT_BRAIN_DETECTED\x10\xa6\x1f\x12\x1d\n" +
	"\x18ERROR_CODE_KEY_NOT_FOUND\x10\x89'\x12\x1c\n" +
	"\x17ERROR_CODE_STORAGE_FULL\x10\x8a'\x12\"\n" +
	"\x1dERROR_CODE_STORAGE_CORRUPTION\x10\x8b'\x12%\n" +
	" ERROR_CODE_HINTED_HANDOFF_FAILED\x10\x8c'\x12\"\n" +
	"\x1dERROR_CODE_INVALID_KEY_FORMAT\x10\x8d'\x12\x1f\n" +
	"\x1aERROR_CODE_VALUE_TOO_LARGE\x10\x8e'\x12\x1c\n" +
	"\x17ERROR_CODE_UNAUTHORIZED\x10\xf1.\x12\x1d\n" +
	"\x18ERROR_CODE_TOKEN_EXPIRED\x10\xf2.\x12!\n" +
	"\x1cERROR_CODE_PERMISSION_DENIED\x10\xf3.\x12#\n" +
	"\x1eERROR_CODE_INVALID_CREDENTIALS\x10\xf4.2?\n" +
	"\tTransport\x122\n" +
	"\rHandleMessage\x12\x0f.MessageRequest\x1a\x10.MessageResponseB2Z0github.com/suohailong/harmoniakv/internal/api/v1b\x06proto3"

var (
	file_internal_api_v1_internal_proto_rawDescOnce sync.Once
	file_internal_api_v1_internal_proto_rawDescData []byte
)

func file_internal_api_v1_internal_proto_rawDescGZIP() []byte {
	file_internal_api_v1_internal_proto_rawDescOnce.Do(func() {
		file_internal_api_v1_internal_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_internal_api_v1_internal_proto_rawDesc), len(file_internal_api_v1_internal_proto_rawDesc)))
	})
	return file_internal_api_v1_internal_proto_rawDescData
}

var file_internal_api_v1_internal_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_internal_api_v1_internal_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_internal_api_v1_internal_proto_goTypes = []any{
	(MessageType)(0),              // 0: MessageType
	(ErrorCode)(0),                // 1: ErrorCode
	(*Error)(nil),                 // 2: Error
	(*KvCommand)(nil),             // 3: KvCommand
	(*GossipMessage)(nil),         // 4: GossipMessage
	(*QuorumRequest)(nil),         // 5: QuorumRequest
	(*NodeJoinRequest)(nil),       // 6: NodeJoinRequest
	(*NodeLeaveRequest)(nil),      // 7: NodeLeaveRequest
	(*AntiEntropyRequest)(nil),    // 8: AntiEntropyRequest
	(*HintedHandoffRequest)(nil),  // 9: HintedHandoffRequest
	(*QuorumAckRequest)(nil),      // 10: QuorumAckRequest
	(*VersionMergeRequest)(nil),   // 11: VersionMergeRequest
	(*ClusterQueryRequest)(nil),   // 12: ClusterQueryRequest
	(*MessageRequest)(nil),        // 13: MessageRequest
	(*KvCommandResponse)(nil),     // 14: KvCommandResponse
	(*GossipResponse)(nil),        // 15: GossipResponse
	(*AntiEntropyResponse)(nil),   // 16: AntiEntropyResponse
	(*HintedHandoffResponse)(nil), // 17: HintedHandoffResponse
	(*QuorumResponse)(nil),        // 18: QuorumResponse
	(*NodeJoinResponse)(nil),      // 19: NodeJoinResponse
	(*NodeLeaveResponse)(nil),     // 20: NodeLeaveResponse
	(*QuorumAckResponse)(nil),     // 21: QuorumAckResponse
	(*VersionMergeResponse)(nil),  // 22: VersionMergeResponse
	(*ClusterQueryResponse)(nil),  // 23: ClusterQueryResponse
	(*MessageResponse)(nil),       // 24: MessageResponse
	nil,                           // 25: Error.DetailsEntry
}
var file_internal_api_v1_internal_proto_depIdxs = []int32{
	25, // 0: Error.details:type_name -> Error.DetailsEntry
	0,  // 1: MessageRequest.message_type:type_name -> MessageType
	3,  // 2: MessageRequest.kv_command:type_name -> KvCommand
	4,  // 3: MessageRequest.gossip_message:type_name -> GossipMessage
	5,  // 4: MessageRequest.quorum_request:type_name -> QuorumRequest
	6,  // 5: MessageRequest.node_join_request:type_name -> NodeJoinRequest
	7,  // 6: MessageRequest.node_leave_request:type_name -> NodeLeaveRequest
	8,  // 7: MessageRequest.anti_entropy_request:type_name -> AntiEntropyRequest
	9,  // 8: MessageRequest.hinted_handoff_request:type_name -> HintedHandoffRequest
	10, // 9: MessageRequest.quorum_ack_request:type_name -> QuorumAckRequest
	11, // 10: MessageRequest.version_merge_request:type_name -> VersionMergeRequest
	12, // 11: MessageRequest.cluster_query_request:type_name -> ClusterQueryRequest
	2,  // 12: MessageResponse.error:type_name -> Error
	14, // 13: MessageResponse.kv_response:type_name -> KvCommandResponse
	15, // 14: MessageResponse.gossip_response:type_name -> GossipResponse
	19, // 15: MessageResponse.node_join_response:type_name -> NodeJoinResponse
	20, // 16: MessageResponse.node_leave_response:type_name -> NodeLeaveResponse
	16, // 17: MessageResponse.anti_entropy_response:type_name -> AntiEntropyResponse
	17, // 18: MessageResponse.hinted_handoff_response:type_name -> HintedHandoffResponse
	21, // 19: MessageResponse.quorum_ack_response:type_name -> QuorumAckResponse
	22, // 20: MessageResponse.version_merge_response:type_name -> VersionMergeResponse
	23, // 21: MessageResponse.cluster_query_response:type_name -> ClusterQueryResponse
	13, // 22: Transport.HandleMessage:input_type -> MessageRequest
	24, // 23: Transport.HandleMessage:output_type -> MessageResponse
	23, // [23:24] is the sub-list for method output_type
	22, // [22:23] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_internal_api_v1_internal_proto_init() }
func file_internal_api_v1_internal_proto_init() {
	if File_internal_api_v1_internal_proto != nil {
		return
	}
	file_internal_api_v1_internal_proto_msgTypes[11].OneofWrappers = []any{
		(*MessageRequest_KvCommand)(nil),
		(*MessageRequest_GossipMessage)(nil),
		(*MessageRequest_QuorumRequest)(nil),
		(*MessageRequest_NodeJoinRequest)(nil),
		(*MessageRequest_NodeLeaveRequest)(nil),
		(*MessageRequest_AntiEntropyRequest)(nil),
		(*MessageRequest_HintedHandoffRequest)(nil),
		(*MessageRequest_QuorumAckRequest)(nil),
		(*MessageRequest_VersionMergeRequest)(nil),
		(*MessageRequest_ClusterQueryRequest)(nil),
	}
	file_internal_api_v1_internal_proto_msgTypes[22].OneofWrappers = []any{
		(*MessageResponse_KvResponse)(nil),
		(*MessageResponse_GossipResponse)(nil),
		(*MessageResponse_NodeJoinResponse)(nil),
		(*MessageResponse_NodeLeaveResponse)(nil),
		(*MessageResponse_AntiEntropyResponse)(nil),
		(*MessageResponse_HintedHandoffResponse)(nil),
		(*MessageResponse_QuorumAckResponse)(nil),
		(*MessageResponse_VersionMergeResponse)(nil),
		(*MessageResponse_ClusterQueryResponse)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_internal_api_v1_internal_proto_rawDesc), len(file_internal_api_v1_internal_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_internal_api_v1_internal_proto_goTypes,
		DependencyIndexes: file_internal_api_v1_internal_proto_depIdxs,
		EnumInfos:         file_internal_api_v1_internal_proto_enumTypes,
		MessageInfos:      file_internal_api_v1_internal_proto_msgTypes,
	}.Build()
	File_internal_api_v1_internal_proto = out.File
	file_internal_api_v1_internal_proto_goTypes = nil
	file_internal_api_v1_internal_proto_depIdxs = nil
}
