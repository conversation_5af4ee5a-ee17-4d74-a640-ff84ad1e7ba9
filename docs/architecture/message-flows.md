# HarmoniaKV 消息流程详细设计

## 文档概述

本文档详细描述了 HarmoniaKV 分布式键值存储系统中各种内部消息类型的流转场景、处理流程和响应机制。

### 消息类型概览

HarmoniaKV 定义了以下9种内部消息类型：

| 消息类型 | 用途 | 触发场景 |
|---------|------|---------|
| MessageTypeGossip | 集群状态同步 | 定期心跳、状态传播 |
| MessageTypeKvCommand | 数据读写操作 | 客户端请求、副本同步 |
| MessageTypeNodeJoin | 节点加入 | 集群扩容、故障恢复 |
| MessageTypeNodeLeave | 节点离开 | 计划下线、故障移除 |
| MessageTypeAntiEntropy | 反熵修复 | 数据一致性检查 |
| MessageTypeHintedHandoff | 暗示移交 | 副本不可用处理 |
| MessageTypeQuorumAck | 仲裁确认 | 读写操作确认 |
| MessageTypeVersionMerge | 版本合并 | 冲突版本解决 |
| MessageTypeClusterQuery | 集群查询 | 监控、运维查询 |

## 集群示例配置

为了具体说明消息流程，我们使用以下3节点集群作为示例：

- **节点 A**: 127.0.0.1:8001 (NodeID: node-A)
- **节点 B**: 127.0.0.1:8002 (NodeID: node-B)  
- **节点 C**: 127.0.0.1:8003 (NodeID: node-C)
- **配置**: Replicas=2, ReadQuorum=1, WriteQuorum=2

---

## 1. MessageTypeGossip - 集群状态同步

### 1.1 使用场景
- 节点定期心跳检测
- 集群成员状态传播
- 新节点发现和故障检测
- 元数据同步

### 1.2 消息流程

#### 场景：节点 A 进行定期 Gossip

**发送方**: 节点 A  
**接收方**: 随机选择的其他节点（例如节点 B）  
**频率**: 每秒一次

```go
// 步骤1：节点 A 发送 Gossip 消息
A → B: {
    MessageType: MessageTypeGossip,
    MessageId: "gossip-001",
    From: "node-A", 
    To: "node-B",
    Data: {
        Source: {
            ID: "node-A",
            Counter: 100,
            HeartTime: 1640995200,
            State: ONLINE
        },
        NodeStates: {
            "node-A": {Counter: 100, HeartTime: 1640995200, State: ONLINE},
            "node-B": {Counter: 98,  HeartTime: 1640995190, State: ONLINE},
            "node-C": {Counter: 99,  HeartTime: 1640995195, State: ONLINE}
        }
    }
}
```

**步骤2：节点 B 处理 Gossip 消息**
```go
func (c *defaultCluster) HandleGossipMessage(gossipMessage *GossipStateMessage) {
    // 更新本地状态表
    for nodeID, nodeState := range gossipMessage.NodeStates {
        if localNode := c.getNode(nodeID); localNode != nil {
            if localNode.Counter < nodeState.Counter {
                // 发现更新的状态，更新本地记录
                localNode.Counter = nodeState.Counter
                localNode.HeartTime = time.Now().Unix()
                localNode.State = nodeState.State
            }
        } else {
            // 发现新节点，添加到本地记录
            c.AddNode(nodeState)
        }
    }
    
    // 检查是否有节点状态差异需要回复
    diff := c.calculateStateDiff(gossipMessage.NodeStates)
    if len(diff) > 0 {
        // 发送差异状态给发送方
        c.sendGossipResponse("node-A", diff)
    }
}
```

**步骤3：节点 B 响应（如有状态差异）**
```go
B → A: {
    MessageType: MessageTypeGossip,
    MessageId: "gossip-002",
    Data: {
        Source: {ID: "node-B", ...},
        NodeStates: {
            // 只发送 A 不知道或版本较旧的状态
            "node-C": {Counter: 101, HeartTime: 1640995210, State: ONLINE}
        }
    }
}
```

### 1.3 故障检测
```go
func (c *defaultCluster) runGossip() {
    // 检查成员列表中node的状态
    for _, n := range c.pnodes {
        t := time.Unix(n.HeartTime, 0)
        // 如果时间超过1分钟没有心跳，标记为离线
        if time.Since(t) > 1*time.Minute {
            n.State = OFFLINE
            logrus.Debugf("Node %s marked as OFFLINE", n.GetId())
        }
    }
}
```

---

## 2. MessageTypeKvCommand - 数据读写操作

### 2.1 使用场景
- 客户端数据写入请求
- 客户端数据读取请求
- 副本节点间数据同步
- 协调节点向副本节点分发操作

### 2.2 写操作流程

#### 场景：客户端向节点 A 写入 key="user:123", value="John Doe"

**步骤1：确定副本位置**
```go
hash := consistentHash("user:123")
replicas := cluster.GetReplicas("user:123", 2)  // 返回 [A, C]
coordinator := replicas[0]  // A 作为协调节点
```

**步骤2：协调节点 A 处理写入**
```go
// A 首先写入本地
localResult := A.HandleCommand(&KvCommand{
    Command: PUT,
    Key: "user:123", 
    Value: {
        Data: "John Doe", 
        VersionVector: {"node-A": 1}
    }
})

// A 发送给副本节点 C
A → C: {
    MessageType: MessageTypeKvCommand,
    MessageId: "write-001",
    From: "node-A",
    To: "node-C", 
    Data: {
        Command: PUT,
        Key: "user:123",
        Value: {
            Data: "John Doe", 
            VersionVector: {"node-A": 1}
        },
        Timestamp: time.Now(),
        OperationId: "write-001"
    }
}
```

**步骤3：副本节点 C 处理写入**
```go
func (c *defaultCluster) HandleKvCommand(ctx context.Context, req *InternalRequest) error {
    var cmd KvCommand
    json.Unmarshal(req.Data.Value, &cmd)
    
    // 更新版本向量并写入本地存储
    cmd.Value.VersionVector.Increment("node-C")  // {"node-A": 1, "node-C": 1}
    err := c.currentNode.Store.Put(cmd.Key, cmd.Value)
    
    // 发送确认响应
    c.sendQuorumAck(req.From, req.MessageId, err == nil, cmd.Value)
    return err
}
```

**步骤4：副本节点 C 发送确认**
```go
C → A: {
    MessageType: MessageTypeQuorumAck,
    MessageId: "ack-001",
    From: "node-C",
    To: "node-A",
    Data: {
        OperationId: "write-001",
        Success: true,
        NodeId: "node-C",
        Value: {
            Data: "John Doe",
            VersionVector: {"node-A": 1, "node-C": 1}
        },
        Timestamp: time.Now()
    }
}
```

**步骤5：协调节点 A 收集仲裁确认**
```go
func (n *Node) HandleQuorumAck(ack *QuorumAckResponse) {
    n.mu.Lock()
    defer n.mu.Unlock()
    
    n.ackCounter[ack.OperationId]++
    
    if n.ackCounter[ack.OperationId] >= config.WriteQuorum() {
        // 达到写仲裁（包括本地写入），操作成功
        n.notifyClient(ack.OperationId, "SUCCESS", ack.Value)
        delete(n.ackCounter, ack.OperationId)
    }
}
```

### 2.3 读操作流程

#### 场景：客户端从节点 B 读取 key="user:123"

**步骤1：节点 B 确定不是副本，转发请求**
```go
replicas := cluster.GetReplicas("user:123", 2)  // [A, C]
isReplica := false
for _, replica := range replicas {
    if replica.ID == "node-B" {
        isReplica = true
        break
    }
}

if !isReplica {
    // 转发给协调节点 A
    B → A: {
        MessageType: MessageTypeKvCommand,
        MessageId: "read-001",
        Data: {
            Command: GET,
            Key: "user:123",
            ClientNode: "node-B"  // 标记原始请求来源
        }
    }
}
```

**步骤2：协调节点 A 向所有副本发送读请求**
```go
// A 读取本地数据
localValue := A.Store.Get("user:123")

// A 向其他副本节点发送读请求
A → C: {
    MessageType: MessageTypeKvCommand,
    MessageId: "read-002",
    Data: {
        Command: GET,
        Key: "user:123",
        OperationId: "read-001"
    }
}
```

**步骤3：副本节点 C 响应**
```go
func (c *defaultCluster) HandleGetCommand(req *GetRequest) {
    value := c.currentNode.Store.Get(req.Key)
    
    // 发送读取结果
    c.trans.Send(ctx, &InternalRequest{
        MessageType: MessageTypeQuorumAck,
        To: req.From,
        Data: {
            OperationId: req.OperationId,
            Success: true,
            Value: value,
            NodeId: c.currentNode.ID
        }
    })
}

C → A: {
    MessageType: MessageTypeQuorumAck,
    Data: {
        OperationId: "read-001",
        Success: true,
        Value: {
            Data: "John Doe",
            VersionVector: {"node-A": 1, "node-C": 1}
        },
        NodeId: "node-C"
    }
}
```

**步骤4：协调节点 A 收集响应并返回**
```go
func (n *Node) CollectReadResponses(operationId string) []*Value {
    responses := []Value{localValue}  // 包含本地读取结果
    
    // 等待其他副本响应
    for i := 0; i < config.ReadQuorum()-1; i++ {
        select {
        case resp := <-n.readResponseChan:
            if resp.OperationId == operationId {
                responses = append(responses, resp.Value)
            }
        case <-time.After(READ_TIMEOUT):
            break
        }
    }
    
    // 检查版本冲突
    if n.hasVersionConflict(responses) {
        // 触发版本合并流程
        return n.mergeVersions(responses)
    }
    
    return responses
}

// A 返回结果给原始请求节点 B
A → B: {
    MessageType: MessageTypeKvCommand,
    Data: {
        Success: true,
        Values: mergedValues,
        OperationId: "read-001"
    }
}
```

---

## 3. MessageTypeNodeJoin - 节点加入

### 3.1 使用场景
- 新节点启动加入集群
- 故障节点恢复重新加入
- 集群扩容操作
- 网络分区恢复后重新加入

### 3.2 消息流程

#### 场景：新节点 D 加入现有3节点集群

**步骤1：节点 D 向种子节点发送加入请求**
```go
// 节点 D 启动时，从配置获取种子节点列表
seedNodes := config.GetSeedNodes()  // ["node-A", "node-B"]

// 向第一个可用的种子节点发送加入请求
D → A: {
    MessageType: MessageTypeNodeJoin,
    MessageId: "join-001",
    From: "node-D",
    To: "node-A",
    Data: {
        NodeInfo: {
            ID: "node-D",
            Address: "127.0.0.1:8004",
            Capabilities: ["storage", "compute"],
            Version: "1.0.0"
        },
        ClusterToken: "harmoniakv-cluster-token"
    }
}
```

**步骤2：种子节点 A 验证并处理加入请求**
```go
func (c *defaultCluster) HandleNodeJoin(ctx context.Context, req *InternalRequest) error {
    var joinReq NodeJoinRequest
    json.Unmarshal(req.Data.Value, &joinReq)
    
    // 验证集群令牌
    if joinReq.ClusterToken != config.GetClusterToken() {
        return c.sendJoinResponse(req.From, false, "Invalid cluster token")
    }
    
    // 检查节点ID是否已存在
    if c.nodeExists(joinReq.NodeInfo.ID) {
        return c.sendJoinResponse(req.From, false, "Node ID already exists")
    }
    
    // 创建新节点并添加到集群
    newNode := NewNode(
        joinReq.NodeInfo.ID,
        joinReq.NodeInfo.Address,
        c,
        c.trans
    )
    c.AddNode(newNode)
    
    // 重新计算一致性哈希环
    c.rebalanceConsistentHashRing()
    
    // 发送成功响应
    return c.sendJoinResponse(req.From, true, "")
}

func (c *defaultCluster) sendJoinResponse(targetNode string, success bool, errorMsg string) error {
    response := NodeJoinResponse{
        Success: success,
        ClusterID: config.GetClusterID(),
        Error: errorMsg,
    }
    
    if success {
        // 包含现有节点信息
        response.ExistingNodes = c.getAllNodeIDs()
        response.ClusterConfig = c.getClusterConfig()
    }
    
    return c.trans.Send(ctx, &InternalRequest{
        MessageType: MessageTypeNodeJoin,  // 响应消息
        To: targetNode,
        Data: response
    })
}
```

**步骤3：种子节点 A 响应加入结果**
```go
A → D: {
    MessageType: MessageTypeNodeJoin,
    MessageId: "join-response-001",
    From: "node-A",
    To: "node-D",
    Data: {
        Success: true,
        ClusterID: "harmoniakv-cluster",
        ExistingNodes: ["node-A", "node-B", "node-C"],
        ClusterConfig: {
            Replicas: 2,
            ReadQuorum: 1,
            WriteQuorum: 2
        },
        NodeRole: "storage"
    }
}
```

**步骤4：新节点 D 处理加入响应**
```go
func (n *Node) HandleJoinResponse(response *NodeJoinResponse) error {
    if !response.Success {
        return fmt.Errorf("Join failed: %s", response.Error)
    }
    
    // 更新本地集群配置
    config.SetClusterID(response.ClusterID)
    config.SetClusterConfig(response.ClusterConfig)
    
    // 添加现有节点到本地集群视图
    for _, nodeID := range response.ExistingNodes {
        if nodeID != n.ID {
            // 这里可能需要额外的节点信息获取
            n.cluster.AddKnownNode(nodeID)
        }
    }
    
    // 开始参与 Gossip 协议
    n.cluster.Start()
    
    return nil
}
```

**步骤5：通过 Gossip 传播加入信息**
```go
// 在下一次 Gossip 周期，A 会将新节点信息传播给其他节点
func (c *defaultCluster) runGossip() {
    // ... 现有逻辑 ...
    
    // 包含新加入的节点信息
    gossipMessage := &GossipStateMessage{
        Source: c.currentNode,
        NodeStates: c.getAllNodeStates(),  // 包含 node-D
    }
    
    randomNodes := c.getRandomNode()
    for _, node := range randomNodes {
        c.sendGossipMessage(node.ID, gossipMessage)
    }
}

// B 和 C 通过 Gossip 了解到新节点 D
A → B: {
    MessageType: MessageTypeGossip,
    Data: {
        NodeStates: {
            "node-D": {
                ID: "node-D",
                Address: "127.0.0.1:8004",
                State: ONLINE,
                HeartTime: time.Now().Unix(),
                Counter: 1
            }
        }
    }
}
```

### 3.3 数据迁移（如果需要）
```go
// 新节点加入后，可能需要迁移部分数据
func (c *defaultCluster) triggerDataMigration(newNodeID string) {
    // 计算需要迁移的 key 范围
    keysToMigrate := c.calculateKeyMigration(newNodeID)
    
    for _, keyRange := range keysToMigrate {
        // 启动数据迁移任务
        go c.migrateKeyRange(keyRange, newNodeID)
    }
}
```

---

## 4. MessageTypeNodeLeave - 节点离开

### 4.1 使用场景
- 节点计划性维护下线
- 集群缩容操作
- 管理员手动移除故障节点
- 节点优雅关闭

### 4.2 消息流程

#### 场景：节点 C 计划维护下线

**步骤1：节点 C 广播离开通知**
```go
// 节点 C 收到停机信号，开始优雅下线流程
func (n *Node) GracefulShutdown() {
    // 停止接受新的客户端请求
    n.stopAcceptingRequests()
    
    // 完成正在处理的请求
    n.waitForOngoingRequests()
    
    // 广播离开消息给所有已知节点
    leaveMessage := &NodeLeaveRequest{
        NodeID: n.ID,
        Reason: "planned_maintenance",
        Timestamp: time.Now(),
    }
    
    for _, node := range n.cluster.GetAllNodes() {
        if node.ID != n.ID {
            n.sendLeaveMessage(node.ID, leaveMessage)
        }
    }
}

C → A: {
    MessageType: MessageTypeNodeLeave,
    MessageId: "leave-001",
    From: "node-C",
    To: "node-A",
    Data: {
        NodeID: "node-C",
        Reason: "planned_maintenance",
        Timestamp: 1640995500,
        DataMigrationRequired: true
    }
}

C → B: {
    MessageType: MessageTypeNodeLeave,
    MessageId: "leave-002",
    From: "node-C",
    To: "node-B",
    Data: {
        NodeID: "node-C",
        Reason: "planned_maintenance",
        Timestamp: 1640995500,
        DataMigrationRequired: true
    }
}
```

**步骤2：其他节点处理离开请求**
```go
func (c *defaultCluster) HandleNodeLeave(ctx context.Context, req *InternalRequest) error {
    var leaveReq NodeLeaveRequest
    json.Unmarshal(req.Data.Value, &leaveReq)
    
    // 标记节点为离开状态
    if node := c.getNode(leaveReq.NodeID); node != nil {
        node.State = LEAVING
        node.LeaveTimestamp = leaveReq.Timestamp
    }
    
    // 如果需要数据迁移，启动迁移流程
    if leaveReq.DataMigrationRequired {
        go c.handleDataMigrationForLeavingNode(leaveReq.NodeID)
    }
    
    // 发送确认响应
    c.sendLeaveAck(req.From, leaveReq.NodeID, true)
    
    // 更新一致性哈希环（移除该节点）
    c.removeNodeFromRing(leaveReq.NodeID)
    
    return nil
}

func (c *defaultCluster) sendLeaveAck(targetNode, leavingNodeID string, success bool) {
    response := &NodeLeaveResponse{
        Success: success,
        LeavingNodeID: leavingNodeID,
        Timestamp: time.Now(),
    }
    
    c.trans.Send(ctx, &InternalRequest{
        MessageType: MessageTypeNodeLeave,
        To: targetNode,
        Data: response
    })
}
```

**步骤3：节点 A 和 B 发送确认响应**
```go
A → C: {
    MessageType: MessageTypeNodeLeave,
    MessageId: "leave-ack-001",
    From: "node-A",
    To: "node-C",
    Data: {
        Success: true,
        LeavingNodeID: "node-C",
        Message: "Data migration initiated",
        MigrationStatus: "in_progress"
    }
}

B → C: {
    MessageType: MessageTypeNodeLeave,
    MessageId: "leave-ack-002", 
    From: "node-B",
    To: "node-C",
    Data: {
        Success: true,
        LeavingNodeID: "node-C",
        Message: "Node removed from cluster view"
    }
}
```

**步骤4：数据迁移流程**
```go
func (c *defaultCluster) handleDataMigrationForLeavingNode(leavingNodeID string) {
    // 确定需要迁移的数据
    keysToMigrate := c.getKeysForNode(leavingNodeID)
    
    for _, key := range keysToMigrate {
        // 计算新的副本位置（排除离开的节点）
        newReplicas := c.getReplicasExcluding(key, leavingNodeID)
        
        // 从离开的节点获取数据
        value := c.getDataFromNode(leavingNodeID, key)
        
        // 迁移到新的副本节点
        for _, replica := range newReplicas {
            c.migrateKeyToNode(key, value, replica.ID)
        }
    }
    
    // 通知离开的节点迁移完成
    c.notifyMigrationComplete(leavingNodeID)
}
```

**步骤5：节点 C 完成下线**
```go
func (n *Node) HandleMigrationComplete() {
    // 收到所有迁移完成确认后，最终下线
    n.logInfo("Data migration completed, shutting down...")
    
    // 从集群中移除自己
    n.cluster.RemoveNode(n)
    
    // 关闭所有连接和服务
    n.transport.Shutdown()
    n.storage.Close()
    
    // 发送最后的下线确认
    n.sendFinalGoodbye()
}
```

---

## 5. MessageTypeHintedHandoff - 暗示移交

### 5.1 使用场景
- 副本节点临时不可用
- 网络分区导致节点隔离
- 节点故障期间的数据暂存
- 故障恢复后的数据同步

### 5.2 消息流程

#### 场景：节点 C 故障，节点 A 代为存储数据

**步骤1：检测副本节点不可用**
```go
func (n *Node) CoordinatePut(ctx context.Context, key []byte, value *version.Value) error {
    replicas := n.cluster.GetReplicas(key, config.Replicas())  // [A, C]
    
    // 尝试写入所有副本
    writeResults := make([]WriteResult, len(replicas))
    
    for i, replica := range replicas {
        if replica.ID == n.ID {
            // 本地写入
            writeResults[i] = n.writeLocal(key, value)
        } else {
            // 远程写入
            writeResults[i] = n.writeRemote(replica.ID, key, value)
        }
    }
    
    // 检查是否有节点不可用
    unavailableNodes := []string{}
    for i, result := range writeResults {
        if !result.Success && result.Error == "node_unreachable" {
            unavailableNodes = append(unavailableNodes, replicas[i].ID)
        }
    }
    
    // 如果有不可用节点，启动 Hinted Handoff
    if len(unavailableNodes) > 0 {
        n.initiateHintedHandoff(key, value, unavailableNodes)
    }
    
    return nil
}
```

**步骤2：选择暗示节点并存储暗示数据**
```go
func (n *Node) initiateHintedHandoff(key []byte, value *version.Value, unavailableNodes []string) {
    for _, unavailableNode := range unavailableNodes {
        // 选择暗示节点（通常是环上的下一个可用节点）
        hintNode := n.cluster.GetNextAvailableNode(unavailableNode)
        
        if hintNode != nil && hintNode.ID != n.ID {
            // 发送暗示数据到暗示节点
            hintData := &HintedHandoffRequest{
                OriginalNodeID: unavailableNode,
                Key: key,
                Value: *value,
                HintTimestamp: time.Now(),
                OperationID: uuid.New().String(),
            }
            
            n.sendHintedHandoff(hintNode.ID, hintData)
        } else {
            // 本地存储暗示数据
            n.storeHintLocally(unavailableNode, key, value)
        }
    }
}

// A 发送暗示数据给 B
A → B: {
    MessageType: MessageTypeHintedHandoff,
    MessageId: "hint-001",
    From: "node-A",
    To: "node-B",
    Data: {
        OriginalNodeID: "node-C",
        Key: "user:789",
        Value: {
            Data: "Alice Smith",
            VersionVector: {"node-A": 5}
        },
        HintTimestamp: 1640995600,
        OperationID: "hint-op-001",
        TTL: 86400  // 24小时过期
    }
}
```

**步骤3：暗示节点存储暗示数据**
```go
func (c *defaultCluster) HandleHintedHandoff(ctx context.Context, req *InternalRequest) error {
    var hintReq HintedHandoffRequest
    json.Unmarshal(req.Data.Value, &hintReq)
    
    // 存储到专门的暗示存储区域
    hint := &HintData{
        OriginalNodeID: hintReq.OriginalNodeID,
        Key: hintReq.Key,
        Value: hintReq.Value,
        Timestamp: hintReq.HintTimestamp,
        TTL: hintReq.TTL,
    }
    
    err := c.hintStorage.Store(hintReq.OriginalNodeID, hint)
    if err != nil {
        c.sendHintResponse(req.From, false, err.Error())
        return err
    }
    
    // 记录暗示统计信息
    c.hintMetrics.IncrementHintsStored(hintReq.OriginalNodeID)
    
    // 发送成功响应
    c.sendHintResponse(req.From, true, "")
    
    return nil
}

func (c *defaultCluster) sendHintResponse(targetNode string, success bool, errorMsg string) {
    response := &HintedHandoffResponse{
        Success: success,
        Error: errorMsg,
        Timestamp: time.Now(),
    }
    
    c.trans.Send(ctx, &InternalRequest{
        MessageType: MessageTypeHintedHandoff,
        To: targetNode,
        Data: response,
    })
}
```

**步骤4：暗示节点确认存储**
```go
B → A: {
    MessageType: MessageTypeHintedHandoff,
    MessageId: "hint-ack-001",
    From: "node-B", 
    To: "node-A",
    Data: {
        Success: true,
        OperationID: "hint-op-001",
        HintCount: 1,
        StorageLocation: "hint-storage-node-C"
    }
}
```

**步骤5：故障节点恢复，接收暗示数据**
```go
// 节点 C 恢复后，B 主动推送暗示数据
func (c *defaultCluster) onNodeRecovery(recoveredNodeID string) {
    // 检查是否有该节点的暗示数据
    hints := c.hintStorage.GetHintsForNode(recoveredNodeID)
    
    if len(hints) > 0 {
        // 分批发送暗示数据
        batchSize := 100
        for i := 0; i < len(hints); i += batchSize {
            end := i + batchSize
            if end > len(hints) {
                end = len(hints)
            }
            
            batch := hints[i:end]
            c.sendHintBatch(recoveredNodeID, batch)
        }
    }
}

B → C: {
    MessageType: MessageTypeHintedHandoff,
    MessageId: "hint-restore-001",
    From: "node-B",
    To: "node-C",
    Data: {
        OperationType: "restore",
        HintedData: [
            {
                Key: "user:789",
                Value: {...},
                OriginalTimestamp: 1640995600
            },
            // ... 更多暗示数据
        ],
        BatchInfo: {
            BatchNumber: 1,
            TotalBatches: 3,
            TotalHints: 250
        }
    }
}
```

**步骤6：恢复节点处理暗示数据并确认**
```go
func (c *defaultCluster) HandleHintRestore(ctx context.Context, req *InternalRequest) error {
    var restoreReq HintRestoreRequest
    json.Unmarshal(req.Data.Value, &restoreReq)
    
    restoredCount := 0
    for _, hintData := range restoreReq.HintedData {
        // 检查本地是否已有更新版本
        localValue := c.currentNode.Store.Get(hintData.Key)
        
        if localValue == nil || c.isHintNewer(hintData.Value, localValue) {
            // 应用暗示数据
            err := c.currentNode.Store.Put(hintData.Key, hintData.Value)
            if err == nil {
                restoredCount++
            }
        }
    }
    
    // 发送恢复确认
    c.sendRestoreAck(req.From, restoredCount, len(restoreReq.HintedData))
    
    return nil
}

C → B: {
    MessageType: MessageTypeHintedHandoff,
    MessageId: "hint-restore-ack-001",
    From: "node-C",
    To: "node-B", 
    Data: {
        Success: true,
        RestoredCount: 98,
        TotalReceived: 100,
        BatchNumber: 1,
        Message: "Batch restored successfully"
    }
}
```

**步骤7：清理暗示数据**
```go
func (c *defaultCluster) cleanupHints(nodeID string) {
    // 所有批次确认后，清理暗示存储
    err := c.hintStorage.DeleteHintsForNode(nodeID)
    if err != nil {
        logrus.Errorf("Failed to cleanup hints for node %s: %v", nodeID, err)
    } else {
        logrus.Infof("Successfully cleaned up hints for node %s", nodeID)
    }
    
    // 更新统计信息
    c.hintMetrics.RecordHintCleanup(nodeID)
}
```

---

## 6. MessageTypeAntiEntropy - 反熵修复

### 6.1 使用场景
- 定期数据一致性检查
- 网络分区恢复后的数据同步
- 检测和修复副本间的数据差异
- 主动发现静默数据损坏

### 6.2 消息流程

#### 场景：节点 A 与节点 C 进行反熵检查

**步骤1：节点 A 发起反熵检查**
```go
func (c *defaultCluster) PeriodicAntiEntropy() {
    // 定期（如每小时）对所有副本进行反熵检查
    ticker := time.NewTicker(1 * time.Hour)
    
    for {
        select {
        case <-ticker.C:
            c.runAntiEntropyCheck()
        }
    }
}

func (c *defaultCluster) runAntiEntropyCheck() {
    // 获取本节点负责的 key 范围
    keyRanges := c.getResponsibleKeyRanges()
    
    for _, keyRange := range keyRanges {
        // 获取该范围的副本节点
        replicas := c.getReplicasForRange(keyRange)
        
        for _, replica := range replicas {
            if replica.ID != c.currentNode.ID {
                c.initiateAntiEntropyWith(replica.ID, keyRange)
            }
        }
    }
}
```

**步骤2：计算并发送 Merkle Tree**
```go
func (c *defaultCluster) initiateAntiEntropyWith(nodeID string, keyRange KeyRange) {
    // 构建该范围的 Merkle Tree
    merkleTree := c.buildMerkleTree(keyRange)
    
    antiEntropyReq := &AntiEntropyRequest{
        KeyRange: keyRange,
        MerkleTreeRoot: merkleTree.Root().Hash,
        TreeNodes: merkleTree.GetAllNodes(),
        NodeID: c.currentNode.ID,
        Timestamp: time.Now(),
    }
    
    // 发送反熵请求
    c.trans.Send(ctx, &InternalRequest{
        MessageType: MessageTypeAntiEntropy,
        From: c.currentNode.ID,
        To: nodeID,
        Data: antiEntropyReq,
    })
}

A → C: {
    MessageType: MessageTypeAntiEntropy,
    MessageId: "anti-entropy-001",
    From: "node-A",
    To: "node-C",
    Data: {
        KeyRange: {
            Start: "user:000",
            End: "user:999"
        },
        MerkleTreeRoot: "abc123def456...",
        TreeNodes: [
            {
                Hash: "root-hash",
                Children: ["left-hash", "right-hash"]
            },
            // ... 更多树节点
        ],
        ChecksumMap: {
            "user:100-user:199": "checksum1",
            "user:200-user:299": "checksum2"
        }
    }
}
```

**步骤3：节点 C 比较 Merkle Tree 并识别差异**
```go
func (c *defaultCluster) HandleAntiEntropy(ctx context.Context, req *InternalRequest) error {
    var antiReq AntiEntropyRequest
    json.Unmarshal(req.Data.Value, &antiReq)
    
    // 构建本地 Merkle Tree
    localTree := c.buildMerkleTree(antiReq.KeyRange)
    
    // 比较 Merkle Tree，找出差异
    diff := c.compareMerkleTrees(localTree, antiReq.TreeNodes)
    
    response := &AntiEntropyResponse{
        NodeID: c.currentNode.ID,
        KeyRange: antiReq.KeyRange,
        ConflictKeys: []string{},
        MissingKeys: []string{},
        ExtraKeys: []string{},
        SyncData: map[string]interface{}{},
    }
    
    if len(diff.ConflictingRanges) > 0 {
        // 获取冲突范围的详细数据
        for _, conflictRange := range diff.ConflictingRanges {
            conflictKeys := c.getKeysInRange(conflictRange)
            
            for _, key := range conflictKeys {
                localValue := c.currentNode.Store.Get(key)
                remoteValue := c.getRemoteValue(antiReq.NodeID, key)
                
                if localValue == nil && remoteValue != nil {
                    response.MissingKeys = append(response.MissingKeys, key)
                } else if localValue != nil && remoteValue == nil {
                    response.ExtraKeys = append(response.ExtraKeys, key)
                    response.SyncData[key] = localValue
                } else if !c.versionsEqual(localValue, remoteValue) {
                    response.ConflictKeys = append(response.ConflictKeys, key)
                    response.SyncData[key] = localValue
                }
            }
        }
    }
    
    // 发送反熵响应
    c.sendAntiEntropyResponse(req.From, response)
    
    return nil
}
```

**步骤4：节点 C 发送比较结果**
```go
C → A: {
    MessageType: MessageTypeAntiEntropy,
    MessageId: "anti-entropy-response-001",
    From: "node-C",
    To: "node-A",
    Data: {
        Success: true,
        KeyRange: {
            Start: "user:000", 
            End: "user:999"
        },
        ConflictKeys: ["user:123", "user:456"],
        MissingKeys: ["user:789"],
        ExtraKeys: [],
        SyncData: {
            "user:123": {
                Data: "John Doe Updated",
                VersionVector: {"node-C": 3, "node-A": 1}
            },
            "user:456": {
                Data: "Jane Smith",
                VersionVector: {"node-C": 2}
            },
            "user:789": {
                Data: "Bob Johnson", 
                VersionVector: {"node-C": 1}
            }
        },
        Statistics: {
            TotalKeysChecked: 1000,
            ConflictsFound: 2,
            MissingKeysFound: 1
        }
    }
}
```

**步骤5：节点 A 处理同步结果**
```go
func (c *defaultCluster) HandleAntiEntropyResponse(response *AntiEntropyResponse) error {
    syncResults := &SyncResults{
        Resolved: 0,
        Failed: 0,
        Conflicts: 0,
    }
    
    // 处理缺失的键
    for _, missingKey := range response.MissingKeys {
        if syncData, exists := response.SyncData[missingKey]; exists {
            err := c.currentNode.Store.Put(missingKey, syncData)
            if err == nil {
                syncResults.Resolved++
            } else {
                syncResults.Failed++
            }
        }
    }
    
    // 处理冲突的键
    for _, conflictKey := range response.ConflictKeys {
        localValue := c.currentNode.Store.Get(conflictKey)
        remoteValue := response.SyncData[conflictKey]
        
        // 使用版本向量解决冲突
        mergedValue := c.resolveVersionConflict(localValue, remoteValue)
        
        if mergedValue != nil {
            err := c.currentNode.Store.Put(conflictKey, mergedValue)
            if err == nil {
                syncResults.Resolved++
                
                // 如果需要，启动版本合并流程
                if c.requiresVersionMerge(localValue, remoteValue) {
                    c.initiateVersionMerge(conflictKey, localValue, remoteValue)
                }
            } else {
                syncResults.Failed++
            }
        } else {
            syncResults.Conflicts++
            // 无法自动解决的冲突，可能需要人工干预或应用程序级解决
            c.logVersionConflict(conflictKey, localValue, remoteValue)
        }
    }
    
    // 记录同步统计信息
    c.recordAntiEntropyResults(response.NodeID, syncResults)
    
    return nil
}
```

**步骤6：双向同步（如果需要）**
```go
// 如果 A 发现 C 缺少一些数据，A 也会发送数据给 C
func (c *defaultCluster) sendSyncData(targetNodeID string, syncData map[string]interface{}) {
    if len(syncData) == 0 {
        return
    }
    
    syncReq := &DataSyncRequest{
        SourceNodeID: c.currentNode.ID,
        SyncData: syncData,
        Timestamp: time.Now(),
    }
    
    c.trans.Send(ctx, &InternalRequest{
        MessageType: MessageTypeAntiEntropy,
        To: targetNodeID,
        Data: syncReq,
    })
}

A → C: {
    MessageType: MessageTypeAntiEntropy,
    MessageId: "sync-data-001",
    From: "node-A",
    To: "node-C",
    Data: {
        OperationType: "data_sync",
        SyncData: {
            "user:999": {
                Data: "Charlie Brown",
                VersionVector: {"node-A": 4}
            }
        },
        Reason: "missing_in_remote"
    }
}
```

---

## 7. MessageTypeQuorumAck - 仲裁确认

### 7.1 使用场景
- 写操作的副本确认
- 读操作的数据返回
- 分布式事务的投票阶段
- 一致性级别的保证机制

### 7.2 消息流程

#### 场景：写操作的仲裁确认（延续 KvCommand 写操作）

**步骤1：副本节点完成写入后发送确认**
```go
func (n *Node) HandleWriteCommand(req *WriteCommandRequest) error {
    // 执行写入操作
    err := n.Store.Put(req.Key, req.Value)
    
    // 生成操作结果
    result := &WriteResult{
        Success: err == nil,
        NodeID: n.ID,
        Timestamp: time.Now(),
        Value: req.Value,
    }
    
    if err != nil {
        result.Error = err.Error()
    }
    
    // 发送 Quorum Ack 给协调节点
    n.sendQuorumAck(req.CoordinatorID, req.OperationID, result)
    
    return err
}

func (n *Node) sendQuorumAck(coordinatorID, operationID string, result *WriteResult) {
    ackData := &QuorumAckRequest{
        OperationID: operationID,
        NodeID: n.ID,
        Success: result.Success,
        Error: result.Error,
        Value: result.Value,
        Timestamp: result.Timestamp,
        Metrics: &OperationMetrics{
            ProcessingTime: result.ProcessingTime,
            StorageLatency: result.StorageLatency,
        },
    }
    
    n.trans.Send(ctx, &InternalRequest{
        MessageType: MessageTypeQuorumAck,
        From: n.ID,
        To: coordinatorID,
        Data: ackData,
    })
}
```

**步骤2：多个副本节点发送确认**
```go
// 节点 A（协调节点）写入本地后，节点 C 完成写入并发送确认
C → A: {
    MessageType: MessageTypeQuorumAck,
    MessageId: "ack-write-001",
    From: "node-C",
    To: "node-A",
    Data: {
        OperationID: "write-op-12345",
        NodeID: "node-C",
        Success: true,
        Value: {
            Data: "John Doe",
            VersionVector: {"node-A": 1, "node-C": 1}
        },
        Timestamp: 1640995700,
        Metrics: {
            ProcessingTime: 5,  // ms
            StorageLatency: 2   // ms
        }
    }
}

// 如果有第三个副本节点 D
D → A: {
    MessageType: MessageTypeQuorumAck,
    MessageId: "ack-write-002", 
    From: "node-D",
    To: "node-A",
    Data: {
        OperationID: "write-op-12345",
        NodeID: "node-D",
        Success: true,
        Value: {
            Data: "John Doe",
            VersionVector: {"node-A": 1, "node-C": 1, "node-D": 1}
        },
        Timestamp: 1640995702
    }
}
```

**步骤3：协调节点收集和处理仲裁确认**
```go
func (n *Node) HandleQuorumAck(ctx context.Context, req *InternalRequest) error {
    var ack QuorumAckRequest
    json.Unmarshal(req.Data.Value, &ack)
    
    n.operationMutex.Lock()
    defer n.operationMutex.Unlock()
    
    // 获取操作状态
    op, exists := n.pendingOperations[ack.OperationID]
    if !exists {
        return fmt.Errorf("operation %s not found", ack.OperationID)
    }
    
    // 记录确认
    op.Acknowledgments[ack.NodeID] = &ack
    op.AckCount++
    
    // 检查是否达到仲裁要求
    if op.Type == "write" && op.AckCount >= config.WriteQuorum() {
        n.handleWriteQuorumAchieved(op)
    } else if op.Type == "read" && op.AckCount >= config.ReadQuorum() {
        n.handleReadQuorumAchieved(op)
    }
    
    // 检查是否超时
    if time.Since(op.StartTime) > op.Timeout {
        n.handleOperationTimeout(op)
    }
    
    return nil
}

func (n *Node) handleWriteQuorumAchieved(op *PendingOperation) {
    // 检查所有确认是否成功
    allSuccess := true
    for _, ack := range op.Acknowledgments {
        if !ack.Success {
            allSuccess = false
            break
        }
    }
    
    if allSuccess {
        // 写操作成功
        n.notifyClientSuccess(op.ClientRequestID, op.FinalValue)
        
        // 记录成功统计
        n.metrics.RecordWriteSuccess(op.Key, time.Since(op.StartTime))
        
        // 清理操作状态
        delete(n.pendingOperations, op.ID)
        
        // 如果还有未确认的副本，继续等待或启动 Hinted Handoff
        if op.AckCount < len(op.TargetReplicas) {
            n.handlePartialQuorum(op)
        }
    } else {
        // 有副本写入失败，需要回滚或重试
        n.handleWriteFailure(op)
    }
}
```

#### 场景：读操作的仲裁确认

**步骤1：副本节点返回读取结果**
```go
func (n *Node) HandleReadCommand(req *ReadCommandRequest) error {
    // 从本地存储读取数据
    value := n.Store.Get(req.Key)
    
    // 准备读取结果
    readResult := &ReadResult{
        NodeID: n.ID,
        Key: req.Key,
        Value: value,
        Success: value != nil,
        Timestamp: time.Now(),
    }
    
    if value == nil {
        readResult.Error = "key_not_found"
    }
    
    // 发送读取结果给协调节点
    n.sendReadAck(req.CoordinatorID, req.OperationID, readResult)
    
    return nil
}

C → A: {
    MessageType: MessageTypeQuorumAck,
    MessageId: "ack-read-001",
    From: "node-C", 
    To: "node-A",
    Data: {
        OperationID: "read-op-67890",
        NodeID: "node-C",
        Success: true,
        ReadResult: {
            Key: "user:123",
            Value: {
                Data: "John Doe Updated",
                VersionVector: {"node-A": 1, "node-C": 3}
            },
            LastModified: 1640995650
        },
        Timestamp: 1640995700
    }
}
```

**步骤2：协调节点处理读取仲裁**
```go
func (n *Node) handleReadQuorumAchieved(op *PendingOperation) {
    readResults := []*ReadResult{}
    
    // 收集所有读取结果（包括本地读取）
    for _, ack := range op.Acknowledgments {
        if ack.Success && ack.ReadResult != nil {
            readResults = append(readResults, ack.ReadResult)
        }
    }
    
    if len(readResults) == 0 {
        n.notifyClientError(op.ClientRequestID, "key_not_found")
        return
    }
    
    // 检查版本一致性
    if n.hasVersionConflicts(readResults) {
        // 启动版本合并流程
        mergedResult := n.resolveReadConflicts(readResults)
        n.notifyClientSuccess(op.ClientRequestID, mergedResult)
        
        // 异步启动反熵修复
        go n.repairVersionConflicts(op.Key, readResults)
    } else {
        // 返回一致的结果
        n.notifyClientSuccess(op.ClientRequestID, readResults[0].Value)
    }
    
    // 清理操作状态
    delete(n.pendingOperations, op.ID)
}
```

#### 场景：处理部分失败的仲裁

**步骤1：部分副本发送失败确认**
```go
// 某个副本节点遇到存储错误
C → A: {
    MessageType: MessageTypeQuorumAck,
    MessageId: "ack-write-error-001",
    From: "node-C",
    To: "node-A", 
    Data: {
        OperationID: "write-op-12345",
        NodeID: "node-C",
        Success: false,
        Error: "storage_full",
        ErrorCode: "STORAGE_ERROR",
        Timestamp: 1640995700,
        Metrics: {
            ProcessingTime: 15,
            FailureReason: "disk_space_exhausted"
        }
    }
}
```

**步骤2：协调节点处理失败情况**
```go
func (n *Node) handleWriteFailure(op *PendingOperation) {
    successCount := 0
    failureNodes := []string{}
    
    for nodeID, ack := range op.Acknowledgments {
        if ack.Success {
            successCount++
        } else {
            failureNodes = append(failureNodes, nodeID)
        }
    }
    
    if successCount >= config.WriteQuorum() {
        // 仍然满足写仲裁，操作成功但需要修复
        n.notifyClientSuccess(op.ClientRequestID, op.FinalValue)
        
        // 异步修复失败的副本
        for _, failedNode := range failureNodes {
            go n.repairFailedReplica(op.Key, op.FinalValue, failedNode)
        }
        
        // 记录部分失败统计
        n.metrics.RecordPartialWriteFailure(op.Key, len(failureNodes))
    } else {
        // 未达到写仲裁，操作失败
        n.notifyClientError(op.ClientRequestID, "insufficient_replicas")
        
        // 可能需要回滚已成功的写入
        n.rollbackPartialWrites(op)
        
        // 记录失败统计
        n.metrics.RecordWriteFailure(op.Key, "quorum_not_met")
    }
    
    // 清理操作状态
    delete(n.pendingOperations, op.ID)
}
```

---

## 8. MessageTypeVersionMerge - 版本合并

### 8.1 使用场景
- 并发写入产生的版本冲突
- 网络分区恢复后的数据冲突
- 反熵检查发现的版本不一致
- 读取时发现多个因果无关版本

### 8.2 消息流程

#### 场景：网络分区恢复后发现版本冲突

**步骤1：读取时发现版本冲突**
```go
func (n *Node) CoordinateGet(ctx context.Context, key []byte) ([]*Object, error) {
    // 从多个副本读取数据
    readResults := n.collectReadResponses(key)
    
    // 检查是否存在版本冲突
    if n.hasVersionConflicts(readResults) {
        // 发现冲突，启动版本合并流程
        mergeRequest := &VersionMergeRequest{
            Key: key,
            ConflictingVersions: n.extractConflictingVersions(readResults),
            CoordinatorID: n.ID,
            MergeOperationID: uuid.New().String(),
            Timestamp: time.Now(),
        }
        
        // 向所有相关副本发送版本合并请求
        replicas := n.cluster.GetReplicas(key, config.Replicas())
        for _, replica := range replicas {
            if replica.ID != n.ID {
                n.sendVersionMergeRequest(replica.ID, mergeRequest)
            }
        }
        
        // 本地执行版本合并
        localMergedVersion := n.mergeVersionsLocally(mergeRequest.ConflictingVersions)
        
        return n.waitForMergeCompletion(mergeRequest.MergeOperationID, localMergedVersion)
    }
    
    return n.extractConsistentValues(readResults), nil
}
```

**步骤2：协调节点发送版本合并请求**
```go
A → B: {
    MessageType: MessageTypeVersionMerge,
    MessageId: "merge-001",
    From: "node-A",
    To: "node-B",
    Data: {
        Key: "user:123",
        MergeOperationID: "merge-op-789",
        ConflictingVersions: [
            {
                Data: "John Doe v1",
                VersionVector: {"node-A": 2, "node-C": 1},
                Timestamp: 1640995000,
                SourceNode: "node-A"
            },
            {
                Data: "John Doe v2", 
                VersionVector: {"node-B": 1, "node-C": 1},
                Timestamp: 1640995100,
                SourceNode: "node-B"
            }
        ],
        MergeStrategy: "last_write_wins",
        ClientContext: {
            ApplicationHint: "user_profile_update"
        }
    }
}

A → C: {
    MessageType: MessageTypeVersionMerge,
    MessageId: "merge-002",
    From: "node-A",
    To: "node-C",
    Data: {
        // 同样的合并请求数据
        Key: "user:123",
        MergeOperationID: "merge-op-789",
        ConflictingVersions: [...],
        MergeStrategy: "last_write_wins"
    }
}
```

**步骤3：副本节点执行版本合并**
```go
func (c *defaultCluster) HandleVersionMerge(ctx context.Context, req *InternalRequest) error {
    var mergeReq VersionMergeRequest
    json.Unmarshal(req.Data.Value, &mergeReq)
    
    // 执行版本合并逻辑
    mergeResult := c.executeVersionMerge(&mergeReq)
    
    // 应用合并结果到本地存储
    if mergeResult.Success {
        err := c.currentNode.Store.Put(mergeReq.Key, mergeResult.MergedValue)
        if err != nil {
            mergeResult.Success = false
            mergeResult.Error = err.Error()
        }
    }
    
    // 发送合并结果给协调节点
    c.sendVersionMergeResponse(req.From, &mergeReq, mergeResult)
    
    return nil
}

func (c *defaultCluster) executeVersionMerge(req *VersionMergeRequest) *MergeResult {
    switch req.MergeStrategy {
    case "last_write_wins":
        return c.mergeByLastWriteWins(req.ConflictingVersions)
    case "semantic_merge":
        return c.mergeSemanticData(req.ConflictingVersions, req.ClientContext)
    case "vector_clock":
        return c.mergeByVectorClock(req.ConflictingVersions)
    default:
        return c.mergeByApplicationLogic(req.ConflictingVersions)
    }
}

func (c *defaultCluster) mergeByLastWriteWins(versions []*Version) *MergeResult {
    var latestVersion *Version
    var latestTime time.Time
    
    for _, version := range versions {
        if version.Timestamp.After(latestTime) {
            latestTime = version.Timestamp
            latestVersion = version
        }
    }
    
    // 创建新的合并版本
    mergedVersion := &Version{
        Data: latestVersion.Data,
        VersionVector: c.mergeVersionVectors(versions),
        Timestamp: time.Now(),
        MergeHistory: c.createMergeHistory(versions),
    }
    
    return &MergeResult{
        Success: true,
        MergedValue: mergedVersion,
        Strategy: "last_write_wins",
        ConflictResolution: "automatic",
    }
}
```

**步骤4：副本节点发送合并结果**
```go
B → A: {
    MessageType: MessageTypeVersionMerge,
    MessageId: "merge-response-001",
    From: "node-B",
    To: "node-A",
    Data: {
        MergeOperationID: "merge-op-789",
        Success: true,
        MergedValue: {
            Data: "John Doe v2",  // 最新写入获胜
            VersionVector: {"node-A": 2, "node-B": 1, "node-C": 1},
            Timestamp: 1640995800,
            MergeHistory: [
                {
                    MergedVersions: 2,
                    Strategy: "last_write_wins",
                    Timestamp: 1640995800
                }
            ]
        },
        MergeStrategy: "last_write_wins",
        ConflictResolution: "automatic",
        Metrics: {
            MergeLatency: 10,  // ms
            ConflictsResolved: 1
        }
    }
}

C → A: {
    MessageType: MessageTypeVersionMerge,
    MessageId: "merge-response-002",
    From: "node-C",
    To: "node-A",
    Data: {
        MergeOperationID: "merge-op-789",
        Success: true,
        MergedValue: {
            // 同样的合并结果
            Data: "John Doe v2",
            VersionVector: {"node-A": 2, "node-B": 1, "node-C": 1},
            Timestamp: 1640995800
        }
    }
}
```

**步骤5：协调节点收集合并结果并完成操作**
```go
func (n *Node) HandleVersionMergeResponse(ctx context.Context, req *InternalRequest) error {
    var mergeResp VersionMergeResponse
    json.Unmarshal(req.Data.Value, &mergeResp)
    
    n.mergeMutex.Lock()
    defer n.mergeMutex.Unlock()
    
    // 获取合并操作状态
    mergeOp, exists := n.pendingMergeOperations[mergeResp.MergeOperationID]
    if !exists {
        return fmt.Errorf("merge operation %s not found", mergeResp.MergeOperationID)
    }
    
    // 记录合并响应
    mergeOp.Responses[req.From] = &mergeResp
    mergeOp.ResponseCount++
    
    // 检查是否收集到足够的响应
    if mergeOp.ResponseCount >= config.WriteQuorum() {
        n.completeMergeOperation(mergeOp)
    }
    
    return nil
}

func (n *Node) completeMergeOperation(mergeOp *PendingMergeOperation) {
    // 验证所有响应的一致性
    consensusValue := n.findMergeConsensus(mergeOp.Responses)
    
    if consensusValue != nil {
        // 达成合并共识
        err := n.Store.Put(mergeOp.Key, consensusValue)
        if err == nil {
            // 通知客户端合并成功
            n.notifyClientMergeSuccess(mergeOp.ClientRequestID, consensusValue)
            
            // 记录合并统计
            n.metrics.RecordVersionMergeSuccess(mergeOp.Key, mergeOp.ConflictCount)
        } else {
            n.notifyClientError(mergeOp.ClientRequestID, "merge_storage_failed")
        }
    } else {
        // 合并冲突，需要人工干预或更复杂的策略
        n.escalateMergeConflict(mergeOp)
    }
    
    // 清理合并操作状态
    delete(n.pendingMergeOperations, mergeOp.ID)
}
```

#### 场景：语义级版本合并

**步骤1：应用级冲突解决**
```go
func (c *defaultCluster) mergeSemanticData(versions []*Version, context *ClientContext) *MergeResult {
    // 根据应用上下文进行语义合并
    switch context.ApplicationHint {
    case "user_profile_update":
        return c.mergeUserProfile(versions)
    case "shopping_cart":
        return c.mergeShoppingCart(versions)
    case "document_edit":
        return c.mergeDocument(versions)
    default:
        return c.mergeByLastWriteWins(versions)
    }
}

func (c *defaultCluster) mergeUserProfile(versions []*Version) *MergeResult {
    // 解析用户配置文件数据
    profiles := make([]*UserProfile, len(versions))
    for i, version := range versions {
        json.Unmarshal(version.Data, &profiles[i])
    }
    
    // 合并逻辑：保留最新的各个字段
    mergedProfile := &UserProfile{}
    latestTimestamps := make(map[string]time.Time)
    
    for _, profile := range profiles {
        for field, value := range profile.Fields {
            if timestamp, exists := profile.FieldTimestamps[field]; exists {
                if !latestTimestamps[field].After(timestamp) {
                    mergedProfile.Fields[field] = value
                    latestTimestamps[field] = timestamp
                }
            }
        }
    }
    
    // 创建合并版本
    mergedData, _ := json.Marshal(mergedProfile)
    mergedVersion := &Version{
        Data: mergedData,
        VersionVector: c.mergeVersionVectors(versions),
        Timestamp: time.Now(),
        MergeStrategy: "semantic_field_merge",
    }
    
    return &MergeResult{
        Success: true,
        MergedValue: mergedVersion,
        Strategy: "semantic_merge",
        ConflictResolution: "field_level_lws",
    }
}
```

**步骤2：处理无法自动解决的冲突**
```go
func (n *Node) escalateMergeConflict(mergeOp *PendingMergeOperation) {
    // 创建冲突报告
    conflictReport := &ConflictReport{
        Key: mergeOp.Key,
        ConflictingVersions: mergeOp.ConflictingVersions,
        FailedMergeAttempts: mergeOp.Responses,
        Timestamp: time.Now(),
        RequiresManualResolution: true,
    }
    
    // 存储到冲突队列，等待人工干预
    n.conflictQueue.Add(conflictReport)
    
    // 通知监控系统
    n.metrics.RecordMergeConflictEscalation(mergeOp.Key)
    
    // 通知客户端冲突无法自动解决
    n.notifyClientConflict(mergeOp.ClientRequestID, conflictReport)
    
    // 发送告警
    n.alerting.SendMergeConflictAlert(conflictReport)
}
```

---

## 9. MessageTypeClusterQuery - 集群查询

### 9.1 使用场景
- 管理员查询集群状态
- 监控系统收集指标
- 负载均衡器更新路由表
- 调试和诊断问题

### 9.2 消息流程

#### 场景：管理员查询集群整体状态

**步骤1：管理员向任意节点发送查询请求**
```go
// 管理员通过 CLI 或 API 发送查询
Admin → A: {
    MessageType: MessageTypeClusterQuery,
    MessageId: "query-001",
    From: "admin-client",
    To: "node-A",
    Data: {
        QueryType: "cluster_status",
        Parameters: {
            IncludeDetails: true,
            IncludeMetrics: true,
            IncludeNodeHealth: true
        },
        Credentials: {
            Token: "admin-token-123",
            Role: "cluster_admin"
        }
    }
}
```

**步骤2：节点 A 验证权限并收集本地信息**
```go
func (c *defaultCluster) HandleClusterQuery(ctx context.Context, req *InternalRequest) error {
    var query ClusterQueryRequest
    json.Unmarshal(req.Data.Value, &query)
    
    // 验证查询权限
    if !c.validateQueryPermissions(query.Credentials, query.QueryType) {
        return c.sendQueryError(req.From, "insufficient_permissions")
    }
    
    switch query.QueryType {
    case "cluster_status":
        return c.handleClusterStatusQuery(req.From, query.Parameters)
    case "node_health":
        return c.handleNodeHealthQuery(req.From, query.Parameters)
    case "data_distribution":
        return c.handleDataDistributionQuery(req.From, query.Parameters)
    case "performance_metrics":
        return c.handlePerformanceQuery(req.From, query.Parameters)
    default:
        return c.sendQueryError(req.From, "unsupported_query_type")
    }
}

func (c *defaultCluster) handleClusterStatusQuery(requester string, params map[string]interface{}) error {
    // 收集本地集群视图
    localStatus := &ClusterStatus{
        NodeID: c.currentNode.ID,
        LocalView: c.getLocalClusterView(),
        Timestamp: time.Now(),
    }
    
    if params["IncludeDetails"].(bool) {
        // 需要从其他节点收集详细信息
        return c.collectDistributedClusterStatus(requester, localStatus)
    } else {
        // 只返回本地视图
        return c.sendClusterQueryResponse(requester, localStatus)
    }
}
```

**步骤3：如果需要详细信息，向其他节点查询**
```go
func (c *defaultCluster) collectDistributedClusterStatus(requester string, localStatus *ClusterStatus) error {
    // 向所有已知节点发送状态查询
    queryID := uuid.New().String()
    nodeQueries := make(map[string]*NodeQuery)
    
    for _, node := range c.pnodes {
        if node.ID != c.currentNode.ID && node.State == ONLINE {
            nodeQuery := &NodeStatusQuery{
                QueryID: queryID,
                RequestedBy: c.currentNode.ID,
                Timestamp: time.Now(),
            }
            
            nodeQueries[node.ID] = nodeQuery
            
            // 发送节点状态查询
            c.trans.Send(ctx, &InternalRequest{
                MessageType: MessageTypeClusterQuery,
                From: c.currentNode.ID,
                To: node.ID,
                Data: nodeQuery,
            })
        }
    }
    
    // 等待收集响应
    go c.waitForNodeStatusResponses(requester, queryID, localStatus, nodeQueries)
    
    return nil
}

// A 向其他节点查询状态
A → B: {
    MessageType: MessageTypeClusterQuery,
    MessageId: "node-query-001",
    From: "node-A",
    To: "node-B",
    Data: {
        QueryType: "node_status",
        QueryID: "status-query-456",
        RequestedBy: "node-A",
        Parameters: {
            IncludeMetrics: true,
            IncludeStorageInfo: true
        }
    }
}

A → C: {
    MessageType: MessageTypeClusterQuery,
    MessageId: "node-query-002", 
    From: "node-A",
    To: "node-C",
    Data: {
        QueryType: "node_status",
        QueryID: "status-query-456",
        RequestedBy: "node-A",
        Parameters: {
            IncludeMetrics: true,
            IncludeStorageInfo: true
        }
    }
}
```

**步骤4：各节点返回状态信息**
```go
func (c *defaultCluster) HandleNodeStatusQuery(ctx context.Context, req *InternalRequest) error {
    var query NodeStatusQuery
    json.Unmarshal(req.Data.Value, &query)
    
    // 收集本节点详细状态
    nodeStatus := &NodeStatus{
        NodeID: c.currentNode.ID,
        State: c.currentNode.State,
        Uptime: time.Since(c.startTime),
        LoadInfo: c.getLoadInfo(),
        StorageInfo: c.getStorageInfo(),
        NetworkInfo: c.getNetworkInfo(),
        Metrics: c.getMetrics(),
        Timestamp: time.Now(),
    }
    
    // 发送状态响应
    return c.sendNodeStatusResponse(req.From, query.QueryID, nodeStatus)
}

B → A: {
    MessageType: MessageTypeClusterQuery,
    MessageId: "node-status-response-001",
    From: "node-B",
    To: "node-A",
    Data: {
        QueryID: "status-query-456",
        NodeStatus: {
            NodeID: "node-B",
            State: "ONLINE",
            Uptime: 86400,  // seconds
            LoadInfo: {
                CPUUsage: 65.5,
                MemoryUsage: 78.2,
                DiskUsage: 45.0
            },
            StorageInfo: {
                TotalKeys: 125000,
                DataSize: "2.5GB",
                IndexSize: "150MB",
                CompressionRatio: 3.2
            },
            NetworkInfo: {
                ConnectionCount: 15,
                InboundThroughput: "50MB/s",
                OutboundThroughput: "45MB/s",
                Latency: 2.5  // ms
            },
            Metrics: {
                ReadOpsPerSec: 1200,
                WriteOpsPerSec: 800,
                AvgReadLatency: 3.2,
                AvgWriteLatency: 5.8,
                ErrorRate: 0.001
            }
        }
    }
}

C → A: {
    MessageType: MessageTypeClusterQuery,
    MessageId: "node-status-response-002",
    From: "node-C",
    To: "node-A",
    Data: {
        QueryID: "status-query-456",
        NodeStatus: {
            NodeID: "node-C",
            State: "ONLINE",
            Uptime: 172800,
            LoadInfo: {
                CPUUsage: 55.2,
                MemoryUsage: 82.1,
                DiskUsage: 52.3
            },
            StorageInfo: {
                TotalKeys: 118000,
                DataSize: "2.3GB",
                IndexSize: "145MB"
            },
            Metrics: {
                ReadOpsPerSec: 1050,
                WriteOpsPerSec: 750,
                AvgReadLatency: 3.8,
                AvgWriteLatency: 6.2,
                ErrorRate: 0.0008
            }
        }
    }
}
```

**步骤5：协调节点汇总并返回完整状态**
```go
func (c *defaultCluster) waitForNodeStatusResponses(requester, queryID string, localStatus *ClusterStatus, nodeQueries map[string]*NodeQuery) {
    responses := make(map[string]*NodeStatus)
    timeout := time.After(5 * time.Second)
    responseCount := 0
    expectedResponses := len(nodeQueries)
    
    for {
        select {
        case response := <-c.nodeStatusResponseChan:
            if response.QueryID == queryID {
                responses[response.NodeID] = response.NodeStatus
                responseCount++
                
                if responseCount >= expectedResponses {
                    // 收集完成，生成完整报告
                    c.generateCompleteClusterReport(requester, localStatus, responses)
                    return
                }
            }
        case <-timeout:
            // 超时，使用已收集的数据
            c.generateCompleteClusterReport(requester, localStatus, responses)
            return
        }
    }
}

func (c *defaultCluster) generateCompleteClusterReport(requester string, localStatus *ClusterStatus, nodeStatuses map[string]*NodeStatus) {
    // 汇总集群状态
    completeStatus := &CompleteClusterStatus{
        ClusterID: config.GetClusterID(),
        TotalNodes: len(c.pnodes),
        OnlineNodes: 0,
        OfflineNodes: 0,
        NodeStatuses: nodeStatuses,
        ClusterMetrics: &ClusterMetrics{},
        DataDistribution: &DataDistribution{},
        HealthScore: 0.0,
        Timestamp: time.Now(),
    }
    
    // 计算汇总指标
    totalReadOps := 0.0
    totalWriteOps := 0.0
    totalKeys := 0
    totalErrors := 0.0
    
    for _, nodeStatus := range nodeStatuses {
        if nodeStatus.State == "ONLINE" {
            completeStatus.OnlineNodes++
            totalReadOps += nodeStatus.Metrics.ReadOpsPerSec
            totalWriteOps += nodeStatus.Metrics.WriteOpsPerSec
            totalKeys += nodeStatus.StorageInfo.TotalKeys
            totalErrors += nodeStatus.Metrics.ErrorRate
        } else {
            completeStatus.OfflineNodes++
        }
    }
    
    completeStatus.ClusterMetrics = &ClusterMetrics{
        TotalReadOpsPerSec: totalReadOps,
        TotalWriteOpsPerSec: totalWriteOps,
        TotalKeys: totalKeys,
        AvgErrorRate: totalErrors / float64(completeStatus.OnlineNodes),
        ClusterLoadBalance: c.calculateLoadBalance(nodeStatuses),
    }
    
    // 计算健康评分
    completeStatus.HealthScore = c.calculateHealthScore(completeStatus)
    
    // 发送完整报告给请求者
    c.sendClusterQueryResponse(requester, completeStatus)
}
```

**步骤6：节点 A 返回完整集群状态给管理员**
```go
A → Admin: {
    MessageType: MessageTypeClusterQuery,
    MessageId: "cluster-status-response-001",
    From: "node-A",
    To: "admin-client",
    Data: {
        Success: true,
        QueryType: "cluster_status",
        ClusterStatus: {
            ClusterID: "harmoniakv-cluster",
            TotalNodes: 3,
            OnlineNodes: 3,
            OfflineNodes: 0,
            NodesInfo: [
                {
                    NodeID: "node-A",
                    Address: "127.0.0.1:8001",
                    State: "ONLINE",
                    Role: "coordinator",
                    Load: "medium"
                },
                {
                    NodeID: "node-B", 
                    Address: "127.0.0.1:8002",
                    State: "ONLINE",
                    Role: "storage",
                    Load: "low"
                },
                {
                    NodeID: "node-C",
                    Address: "127.0.0.1:8003", 
                    State: "ONLINE",
                    Role: "storage",
                    Load: "medium"
                }
            ],
            ClusterMetrics: {
                TotalReadOpsPerSec: 2250,
                TotalWriteOpsPerSec: 1550,
                TotalKeys: 243000,
                AvgErrorRate: 0.0009,
                DataDistribution: {
                    BalanceScore: 0.95,
                    HotspotCount: 0,
                    UnderreplicatedKeys: 0
                }
            },
            HealthScore: 0.98,
            LastUpdated: "2024-01-01T12:00:00Z"
        }
    }
}
```

#### 场景：监控系统查询性能指标

**步骤1：监控系统定期查询**
```go
Monitor → A: {
    MessageType: MessageTypeClusterQuery,
    MessageId: "metrics-query-001",
    From: "monitoring-system",
    To: "node-A",
    Data: {
        QueryType: "performance_metrics",
        Parameters: {
            TimeRange: {
                Start: "2024-01-01T11:00:00Z",
                End: "2024-01-01T12:00:00Z"
            },
            Granularity: "5m",
            Metrics: ["read_latency", "write_latency", "throughput", "error_rate"]
        }
    }
}
```

**步骤2：返回性能指标**
```go
A → Monitor: {
    MessageType: MessageTypeClusterQuery,
    MessageId: "metrics-response-001",
    From: "node-A",
    To: "monitoring-system",
    Data: {
        Success: true,
        QueryType: "performance_metrics",
        Metrics: {
            TimeRange: {
                Start: "2024-01-01T11:00:00Z",
                End: "2024-01-01T12:00:00Z"
            },
            DataPoints: [
                {
                    Timestamp: "2024-01-01T11:00:00Z",
                    ReadLatency: 3.2,
                    WriteLatency: 5.8,
                    ReadThroughput: 1200,
                    WriteThroughput: 800,
                    ErrorRate: 0.001
                },
                // ... 更多时间点数据
            ],
            Aggregates: {
                AvgReadLatency: 3.5,
                AvgWriteLatency: 6.1,
                MaxReadLatency: 15.2,
                MaxWriteLatency: 25.6,
                TotalOperations: 720000
            }
        }
    }
}
```

---

## 总结

以上详细描述了 HarmoniaKV 中9种消息类型的完整流程，包括：

1. **触发场景**：什么情况下发送消息
2. **参与节点**：谁发送、谁接收、谁响应
3. **消息格式**：具体的数据结构和字段
4. **处理逻辑**：接收方如何处理消息
5. **响应机制**：如何返回结果和错误处理
6. **故障处理**：异常情况的处理流程

这些消息流程体现了 Dynamo 风格分布式系统的核心特性：
- **最终一致性**：通过版本向量和反熵修复
- **高可用性**：通过副本、仲裁和暗示移交
- **分区容错**：通过 Gossip 协议和故障检测
- **可扩展性**：通过一致性哈希和动态节点管理

每种消息类型都有明确的语义和处理流程，确保了系统的可靠性和可维护性。 