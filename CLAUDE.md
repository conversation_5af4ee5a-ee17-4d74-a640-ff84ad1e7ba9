# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 提供在此代码库中工作的指导。

## 项目概述

一个全面的分布式系统学习实验室，实现了构建可扩展、容错分布式应用的关键模式和算法。代码库专注于生产级分布式系统概念的教育性实现。

## 架构与核心组件

### 核心系统

1. **Harmoniakv** - Dynamo 风格的分布式键值存储，具备：
   - 一致性哈希实现数据分布
   - Gossip 协议用于集群成员管理
   - 版本向量解决冲突
   - 可调的读写仲裁
   - 预写日志保证持久性

2. **分布式锁** - 基于 Redis 的分布式锁，具备：
   - 可重入锁支持
   - 通过 FIFO 队列实现公平调度
   - 通过 TTL 自动过期
   - Lua 脚本保证原子操作

3. **领导者选举** - 多种实现方式：
   - Raft 共识算法
   - ETCD 领导者选举
   - 带故障检测的状态机

4. **一致性哈希** - 基于环的实现，带虚拟节点

5. **Paxos** - 基础 Paxos 共识算法文档

6. **限流器** - 高性能分布式限流（10万+ TPS）

7. **WAL** - 预写日志，支持分段文件和 CRC 验证

## 构建与开发命令

### 基础命令

```bash
# 构建主 harmoniakv 服务器
cd harmoniakv && make build

# 运行所有测试
go test ./...

# 运行特定组件测试
go test ./harmoniakv/test/...
go test ./distribute_lock/...
go test ./election/...

# 生成 protobuf 定义
cd harmoniakv && make generate

# 清理构建产物
cd harmoniakv && make clean
```

### Docker 命令

```bash
# 构建并标记 Docker 镜像
cd harmoniakv && make image

# 发布到注册表
cd harmoniakv && make publish
```

## 关键入口点

- **Harmoniakv 服务器**: `harmoniakv/main.go`
- **分布式锁示例**: `test/distribute_lock/distribute_lock_test.go`
- **Raft 选举**: `election/raft/raft.go`
- **一致性哈希**: `consistent_hash/consistent-hash.go`

## 配置模式

- **环境变量**: 用于运行时配置
- **单例配置**: 使用 sync.Once 初始化的全局配置实例
- **默认值**: 合理的默认值，支持覆盖

## 测试策略

- **单元测试**: 单个组件测试
- **集成测试**: `test/` 目录中的跨组件测试
- **测试数据**: 位于 `testdata/` 子目录
- **模拟**: 使用 supermonkey 进行函数模拟

## 依赖项

- **Go 1.21+** 必需
- **Redis** 用于分布式锁
- **ETCD** 用于领导者选举和协调
- **BadgerDB** 用于本地存储
- **gRPC** 用于节点间通信

## 代码组织

```
├── harmoniakv/          # 主分布式 KV 存储
├── distribute_lock/     # 分布式锁机制
├── election/           # 领导者选举实现
├── consistent_hash/    # 一致性哈希实现
├── paxos/              # Paxos 共识算法
├── rate_limiter/       # 限流器文档
├── wal/                # 预写日志
└── test/               # 集成测试套件
```

## 常见开发任务

1. **添加新的分布式系统模式**: 创建新的顶级目录
2. **修改 harmoniakv**: 关注 harmoniakv/ 目录结构
3. **测试分布式组件**: 使用 test/ 目录中的现有测试模式
4. **协议缓冲区更改**: 修改 .proto 后在 harmoniakv/ 中运行 `make generate`