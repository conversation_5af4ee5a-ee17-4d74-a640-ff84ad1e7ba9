version: '3.8'

services:
  # Redis 用于分布式锁
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # ETCD 用于领导者选举
  etcd:
    image: quay.io/coreos/etcd:v3.5.13
    ports:
      - "2379:2379"
      - "2380:2380"
    environment:
      - ETCD_NAME=etcd0
      - ETCD_ADVERTISE_CLIENT_URLS=http://0.0.0.0:2379
      - ETCD_LISTEN_CLIENT_URLS=http://0.0.0.0:2379
      - ETCD_INITIAL_ADVERTISE_PEER_URLS=http://0.0.0.0:2380
      - ETCD_LISTEN_PEER_URLS=http://0.0.0.0:2380
      - ETCD_INITIAL_CLUSTER=etcd0=http://0.0.0.0:2380
      - ETCD_INITIAL_CLUSTER_STATE=new
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Prometheus 用于监控
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./scripts/prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'

  # Grafana 用于可视化
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      - prometheus

  # 用于测试的节点
  harmoniakv-node1:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8081:8080"
    environment:
      - NODE_ID=node1
      - NODE_ADDR=harmoniakv-node1:8080
      - PEERS=harmoniakv-node2:8080,harmoniakv-node3:8080
    depends_on:
      - redis
      - etcd

  harmoniakv-node2:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8082:8080"
    environment:
      - NODE_ID=node2
      - NODE_ADDR=harmoniakv-node2:8080
      - PEERS=harmoniakv-node1:8080,harmoniakv-node3:8080
    depends_on:
      - redis
      - etcd

  harmoniakv-node3:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8083:8080"
    environment:
      - NODE_ID=node3
      - NODE_ADDR=harmoniakv-node3:8080
      - PEERS=harmoniakv-node1:8080,harmoniakv-node2:8080
    depends_on:
      - redis
      - etcd

volumes:
  redis_data:
  grafana_data:

networks:
  default:
    driver: bridge