---
alwaysApply: true
---

# Cursor Rules for distribute-learning-lab/harmoniakv

## 通用规则
- 所有响应必须使用中文。
- Git 提交信息格式：前缀: 英文内容，例如 feat: add new feature 或 fix: resolve gossip sending issue。
- 优先使用工具（如 edit_file、read_file）进行代码修改，避免手动输出代码。

## 代码风格
- Go 代码：遵守 go fmt 格式，使用 go vet 检查。
- 测试：
  - 每个模块至少 70% 覆盖率，
  - 使用 testify/suite。接口类型请使用mokgen(github.com/uber-go/mock)先生成对应的mock文件，然后使用mock， 非接口类型的mock使用supermonkey(github.com/cch123/supermonkey)
  - 充分考虑边界情况和错误处理。
- 文档：每个文件头部添加注释，模块级使用 Markdown（如 docs/）。

## 项目指南
- 开发焦点：高扩展性（动态节点）、高可用（Quorum、Handoff）、最终一致性（版本向量）。
- 模块解耦：Gossip 只负责消息内容，Transport 负责发送协议（gRPC/HTTP）。
- 开源准备：添加 LICENSE、更新 README。
- 如果不确定，优先收集信息（如 codebase_search）。 # Cursor Rules for distribute-learning-lab/harmoniakv
