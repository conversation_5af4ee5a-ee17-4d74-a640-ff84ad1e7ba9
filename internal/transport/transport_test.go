package transport

import (
	"context"
	"errors"
	"net"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	internal "github.com/suohailong/harmoniakv/internal/api/v1"
	"github.com/suohailong/harmoniakv/internal/config"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/anypb"
)

// MockInternalHarmoniakvClient 是生成的mock客户端
type MockInternalHarmoniakvClient struct {
	mock.Mock
}

func (m *MockInternalHarmoniakvClient) InternalCommand(ctx context.Context, in *internal.InternalRequest, opts ...grpc.CallOption) (*internal.InternalResponse, error) {
	args := m.Called(ctx, in, opts)
	return args.Get(0).(*internal.InternalResponse), args.Error(1)
}

// MockClientConn 模拟gRPC连接
type MockClientConn struct {
	mock.Mock
}

func (m *MockClientConn) Invoke(ctx context.Context, method string, args interface{}, reply interface{}, opts ...grpc.CallOption) error {
	mockArgs := m.Called(ctx, method, args, reply, opts)
	return mockArgs.Error(0)
}

func (m *MockClientConn) NewStream(ctx context.Context, desc *grpc.StreamDesc, method string, opts ...grpc.CallOption) (grpc.ClientStream, error) {
	mockArgs := m.Called(ctx, desc, method, opts)
	return mockArgs.Get(0).(grpc.ClientStream), mockArgs.Error(1)
}

func (m *MockClientConn) Close() error {
	args := m.Called()
	return args.Error(0)
}

// TransportTestSuite 测试套件
type TransportTestSuite struct {
	suite.Suite
	transporter  *defaultTransporter
	mockClient   *MockInternalHarmoniakvClient
	mockConn     *MockClientConn
	testRequest  *internal.InternalRequest
	testResponse *internal.InternalResponse
}

func (suite *TransportTestSuite) SetupTest() {
	// 初始化配置以避免 nil pointer 问题
	config.Init("")

	// 创建测试用的transporter
	suite.transporter = NewTransporter().(*defaultTransporter)

	// 创建mock对象
	suite.mockClient = new(MockInternalHarmoniakvClient)
	suite.mockConn = new(MockClientConn)

	// 创建测试用的请求和响应
	data, _ := anypb.New(&internal.InternalRequest{
		Token: "test",
	})

	suite.testRequest = &internal.InternalRequest{
		MessageId:   uuid.New().String(),
		MessageType: internal.MessageType_MessageTypeKvCommand,
		From:        "127.0.0.1:8001",
		To:          "127.0.0.1:8002",
		Data:        data,
	}

	suite.testResponse = &internal.InternalResponse{
		MessageId: suite.testRequest.MessageId,
		Data:      data,
	}
}

func (suite *TransportTestSuite) TearDownTest() {
	// 清理资源
	if suite.transporter.pool != nil {
		suite.transporter.pool.Release()
	}
	if suite.transporter.udpConn != nil {
		suite.transporter.udpConn.Close()
	}
}

// TestNewTransporter 测试构造函数
func (suite *TransportTestSuite) TestNewTransporter() {
	t := NewTransporter()
	assert.NotNil(suite.T(), t)

	dt := t.(*defaultTransporter)
	assert.NotNil(suite.T(), dt.pool)
	assert.NotNil(suite.T(), dt.lru)
	assert.NotNil(suite.T(), dt.ch)
	assert.NotNil(suite.T(), dt.responses)
	assert.NotNil(suite.T(), dt.handlers)
	assert.Equal(suite.T(), 100, cap(dt.ch))
}

// TestSend_LocalAddress 测试发送到本地地址的错误情况
func (suite *TransportTestSuite) TestSend_LocalAddress() {
	// 使用当前配置的本地地址
	localAddr := config.NodeAddr()
	req := &internal.InternalRequest{
		To: localAddr,
	}

	_, err := suite.transporter.Send(context.Background(), req)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "local send not supported")
}

// TestSend_Success 测试成功发送 - 这个测试需要重构因为无法简单mock grpc.DialContext
func (suite *TransportTestSuite) TestSend_Success() {
	// 跳过这个测试，因为需要重构代码以支持依赖注入
	suite.T().Skip("需要重构 Send 方法以支持依赖注入")
}

// TestAsyncSend_Success 测试异步发送成功
func (suite *TransportTestSuite) TestAsyncSend_Success() {
	ctx := context.Background()

	// 测试没有MessageId的情况，应该自动生成
	// 使用一个非本地地址来避免 "local send not supported" 错误
	req := &internal.InternalRequest{
		MessageId:   uuid.New().String(),
		MessageType: internal.MessageType_MessageTypeKvCommand,
		From:        "127.0.0.1:8001",
		To:          "127.0.0.1:9999", // 使用不同的端口避免本地地址检查
	}

	// AsyncSend 会提交任务到协程池，不会立即返回错误
	err := suite.transporter.AsyncSend(ctx, req)
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), req.MessageId)

	// 等待一点时间让异步任务完成
	time.Sleep(50 * time.Millisecond)
	responses := suite.transporter.WaitResponses(ctx, 1)
	assert.NotNil(suite.T(), responses)
	assert.Len(suite.T(), responses, 1)
	response := responses[req.MessageId]
	assert.NotNil(suite.T(), response)
	// 验证响应包含正确的 MessageId
	assert.Equal(suite.T(), req.MessageId, response.MessageId)
}

// TestWaitResponses_Timeout 测试等待响应超时
func (suite *TransportTestSuite) TestWaitResponses_Timeout() {
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()

	responses := suite.transporter.WaitResponses(ctx, 5)
	assert.NotNil(suite.T(), responses)
	assert.Len(suite.T(), responses, 0) // 超时，没有响应
}

// TestWaitResponses_Success 测试成功等待响应
func (suite *TransportTestSuite) TestWaitResponses_Success() {
	ctx := context.Background()

	// 预先添加一些响应到map中
	messageId1 := uuid.New().String()
	messageId2 := uuid.New().String()

	// 为每个 messageId 创建对应的响应
	response1 := &internal.InternalResponse{
		MessageId: messageId1,
		Data:      suite.testResponse.Data,
	}
	response2 := &internal.InternalResponse{
		MessageId: messageId2,
		Data:      suite.testResponse.Data,
	}

	suite.transporter.mu.Lock()
	suite.transporter.responses[messageId1] = response1
	suite.transporter.responses[messageId2] = response2
	suite.transporter.mu.Unlock()

	// 模拟channel信号
	go func() {
		time.Sleep(10 * time.Millisecond)
		suite.transporter.ch <- response1
	}()

	responses := suite.transporter.WaitResponses(ctx, 2)
	assert.Len(suite.T(), responses, 2)

	// 验证每个响应都有正确的 MessageId
	assert.Equal(suite.T(), messageId1, responses[messageId1].MessageId)
	assert.Equal(suite.T(), messageId2, responses[messageId2].MessageId)
}

// TestBroadcastUDP_Success 测试UDP广播成功
func (suite *TransportTestSuite) TestBroadcastUDP_Success() {
	ctx := context.Background()

	// 创建一个测试用的UDP连接
	addr, err := net.ResolveUDPAddr("udp", "127.0.0.1:0")
	assert.NoError(suite.T(), err)

	conn, err := net.ListenUDP("udp", addr)
	assert.NoError(suite.T(), err)
	defer conn.Close()

	suite.transporter.udpConn = conn

	targets := []string{"127.0.0.1:9001", "127.0.0.1:9002"}
	err = suite.transporter.BroadcastUDP(ctx, suite.testRequest, targets)

	// 这里不验证错误，因为目标地址可能不存在，但方法应该正常执行
	assert.NotEmpty(suite.T(), suite.testRequest.MessageId)
}

// TestBroadcastUDP_WithContextCancel 测试上下文取消
func (suite *TransportTestSuite) TestBroadcastUDP_WithContextCancel() {
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // 立即取消

	targets := []string{"127.0.0.1:9001"}
	err := suite.transporter.BroadcastUDP(ctx, suite.testRequest, targets)
	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), context.Canceled, err)
}

// TestRegisterHandler 测试注册处理器
func (suite *TransportTestSuite) TestRegisterHandler() {
	handler := func(ctx context.Context, req *internal.InternalRequest) error {
		return nil
	}

	suite.transporter.RegisterHandler(internal.MessageType_MessageTypeKvCommand, handler)

	// 验证处理器已注册
	suite.transporter.handlerMu.RLock()
	_, exists := suite.transporter.handlers[internal.MessageType_MessageTypeKvCommand]
	suite.transporter.handlerMu.RUnlock()

	assert.True(suite.T(), exists)
}

// TestReceive_Success 测试成功接收消息
func (suite *TransportTestSuite) TestReceive_Success() {
	ctx := context.Background()
	called := false

	handler := func(ctx context.Context, req *internal.InternalRequest) error {
		called = true
		assert.Equal(suite.T(), suite.testRequest, req)
		return nil
	}

	suite.transporter.RegisterHandler(suite.testRequest.MessageType, handler)

	err := suite.transporter.Receive(ctx, suite.testRequest)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), called)
}

// TestReceive_NoHandler 测试没有注册处理器的情况
func (suite *TransportTestSuite) TestReceive_NoHandler() {
	ctx := context.Background()

	err := suite.transporter.Receive(ctx, suite.testRequest)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "no handler registered")
}

// TestReceive_HandlerError 测试处理器返回错误
func (suite *TransportTestSuite) TestReceive_HandlerError() {
	ctx := context.Background()
	expectedError := errors.New("handler error")

	handler := func(ctx context.Context, req *internal.InternalRequest) error {
		return expectedError
	}

	suite.transporter.RegisterHandler(suite.testRequest.MessageType, handler)

	err := suite.transporter.Receive(ctx, suite.testRequest)
	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), expectedError, err)
}

// TestConcurrentAccess 测试并发访问
func (suite *TransportTestSuite) TestConcurrentAccess() {
	numGoroutines := 10

	// 测试并发注册处理器
	for i := 0; i < numGoroutines; i++ {
		go func(i int) {
			messageType := internal.MessageType(i % 9) // 使用不同的消息类型
			handler := func(ctx context.Context, req *internal.InternalRequest) error {
				return nil
			}
			suite.transporter.RegisterHandler(messageType, handler)
		}(i)
	}

	// 测试并发访问responses map
	for i := 0; i < numGoroutines; i++ {
		go func(i int) {
			messageId := uuid.New().String()
			suite.transporter.mu.Lock()
			suite.transporter.responses[messageId] = suite.testResponse
			suite.transporter.mu.Unlock()
		}(i)
	}

	time.Sleep(100 * time.Millisecond) // 等待所有goroutine完成

	// 验证没有死锁或panic
	assert.True(suite.T(), true)
}

// TestEdgeCases 测试边界情况
func (suite *TransportTestSuite) TestEdgeCases() {
	ctx := context.Background()

	// 测试nil request
	err := suite.transporter.Receive(ctx, nil)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "request cannot be nil")

	// 测试空的targets列表
	err = suite.transporter.BroadcastUDP(ctx, suite.testRequest, []string{})
	assert.NoError(suite.T(), err)

	// 测试无效的UDP地址
	err = suite.transporter.BroadcastUDP(ctx, suite.testRequest, []string{"invalid-address"})
	assert.NoError(suite.T(), err) // 方法应该继续执行，只是跳过无效地址
}

// 运行测试套件
func TestTransportTestSuite(t *testing.T) {
	suite.Run(t, new(TransportTestSuite))
}
