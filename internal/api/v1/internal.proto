// pkg/api/internal/internal.proto
syntax = "proto3";

option go_package = "github.com/suohailong/harmoniakv/internal/api/v1";

enum MessageType {
  MessageTypeGossip = 0;
  MessageTypeKvCommand = 1;
  MessageTypeNodeJoin = 2;
  MessageTypeNodeLeave = 3;
  MessageTypeAntiEntropy = 4;
  MessageTypeHintedHandoff = 5;
  MessageTypeQuorumAck = 6;
  MessageTypeVersionMerge = 7;
  MessageTypeClusterQuery = 8;
}

// 应用层错误代码
// 注意：纯传输层错误（如网络连接失败）应该通过 gRPC error 返回，不在此处定义
enum ErrorCode {
  // === 成功和通用错误 ===
  ERROR_CODE_SUCCESS = 0;
  ERROR_CODE_UNKNOWN = 1;
  ERROR_CODE_INVALID_REQUEST = 2;
  ERROR_CODE_TIMEOUT = 3;                    // 应用层超时（非网络超时）
  
  // === Quorum 和分布式协调错误 (2000-2999) ===
  ERROR_CODE_QUORUM_READ_FAILED = 2001;      // 读取Quorum失败
  ERROR_CODE_QUORUM_WRITE_FAILED = 2002;     // 写入Quorum失败  
  ERROR_CODE_INSUFFICIENT_REPLICAS = 2003;   // 可用副本不足
  ERROR_CODE_COORDINATOR_UNAVAILABLE = 2004; // 协调节点不可用
  ERROR_CODE_REPLICA_NOT_RESPONDING = 2005;  // 副本节点无响应（应用层检测）
  
  // === 版本冲突和一致性错误 (3000-3999) ===
  ERROR_CODE_VERSION_CONFLICT = 3001;        // 版本向量冲突
  ERROR_CODE_MERGE_FAILED = 3002;           // 版本合并失败
  ERROR_CODE_ANTI_ENTROPY_FAILED = 3003;    // 反熵同步失败
  ERROR_CODE_STALE_READ = 3004;             // 读取到过期数据
  ERROR_CODE_CONCURRENT_MODIFICATION = 3005; // 并发修改冲突
  
  // === 集群管理和成员错误 (4000-4999) ===
  ERROR_CODE_NODE_JOIN_FAILED = 4001;       // 节点加入失败
  ERROR_CODE_NODE_LEAVE_FAILED = 4002;      // 节点离开失败
  ERROR_CODE_GOSSIP_FAILED = 4003;          // Gossip协议失败
  ERROR_CODE_CLUSTER_STATE_INCONSISTENT = 4004; // 集群状态不一致
  ERROR_CODE_MEMBERSHIP_CHANGE_REJECTED = 4005;  // 成员变更被拒绝
  ERROR_CODE_SPLIT_BRAIN_DETECTED = 4006;   // 检测到脑裂
  
  // === 存储和数据错误 (5000-5999) ===
  ERROR_CODE_KEY_NOT_FOUND = 5001;          // 键不存在
  ERROR_CODE_STORAGE_FULL = 5002;           // 存储空间不足
  ERROR_CODE_STORAGE_CORRUPTION = 5003;     // 数据损坏
  ERROR_CODE_HINTED_HANDOFF_FAILED = 5004;  // 暗示移交失败
  ERROR_CODE_INVALID_KEY_FORMAT = 5005;     // 键格式无效
  ERROR_CODE_VALUE_TOO_LARGE = 5006;        // 值过大
  
  // === 认证和授权错误 (6000-6999) ===
  ERROR_CODE_UNAUTHORIZED = 6001;           // 未授权
  ERROR_CODE_TOKEN_EXPIRED = 6002;          // Token过期
  ERROR_CODE_PERMISSION_DENIED = 6003;      // 权限不足
  ERROR_CODE_INVALID_CREDENTIALS = 6004;    // 凭据无效
}

message Error {
  int32 code = 1;                      // 使用 ErrorCode 枚举值
  string message = 2;                  // 人类可读的错误消息
  map<string, string> details = 3;     // 结构化错误详情
  string category = 4;                 // 错误分类：quorum, consistency, cluster, storage, auth
  bool retryable = 5;                  // 是否可重试
  int32 retry_after_seconds = 6;       // 建议重试间隔（秒）
}

message KvCommand {
}

message GossipMessage {
}

message QuorumRequest {
}

message NodeJoinRequest {
}

message NodeLeaveRequest {
}

message AntiEntropyRequest {
}

message HintedHandoffRequest {
}

message QuorumAckRequest {
}

message VersionMergeRequest {
}

message ClusterQueryRequest {
}

message MessageRequest {
  string token = 1;                    // 用于认证
  string request_id = 2;               // 对应 Message.ID
  MessageType message_type = 3;        // 对应 Message.MessageType
  string from = 4;                     // 对应 Message.From
  string to = 5;                       // 对应 Message.To
  oneof request {
    KvCommand kv_command = 6;
    GossipMessage gossip_message=7;
    QuorumRequest quorum_request=8;
    NodeJoinRequest node_join_request=9;
    NodeLeaveRequest node_leave_request=10;
    AntiEntropyRequest anti_entropy_request=11;
    HintedHandoffRequest hinted_handoff_request=12;
    QuorumAckRequest quorum_ack_request=13;
    VersionMergeRequest version_merge_request=14;
    ClusterQueryRequest cluster_query_request=15;
  }        // 对应 Message.Data
}

message KvCommandResponse {
}

message GossipResponse {
}
message AntiEntropyResponse {
}

message HintedHandoffResponse {
}

message QuorumResponse {
}

message NodeJoinResponse {
}

message NodeLeaveResponse {
}

message QuorumAckResponse {
}

message VersionMergeResponse {
}

message ClusterQueryResponse {
}

message MessageResponse {
  string request_id = 1;               // 对应 Message.ID
  bool success = 3;
  Error error = 4;
  oneof response {
    KvCommandResponse kv_response = 10;
    GossipResponse gossip_response = 11;
    NodeJoinResponse node_join_response = 12;
    NodeLeaveResponse node_leave_response = 13;
    AntiEntropyResponse anti_entropy_response = 14;
    HintedHandoffResponse hinted_handoff_response = 15;
    QuorumAckResponse quorum_ack_response = 16;
    VersionMergeResponse version_merge_response = 17;
    ClusterQueryResponse cluster_query_response = 18;
  }
}

service Transport {
  rpc HandleMessage(MessageRequest) returns (MessageResponse);
} 