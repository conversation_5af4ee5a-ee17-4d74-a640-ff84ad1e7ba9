# 一致性哈希详解教程

本教程将带你深入理解一致性哈希的原理和实现，并通过实际代码掌握这一重要的分布式系统技术。

## 📖 什么是哈希？

在传统的哈希表中，我们使用哈希函数将键映射到固定数量的桶（bucket）中。但是当桶的数量发生变化时，几乎所有的键都需要重新映射，这在分布式系统中是不可接受的。

### 传统哈希的问题

假设有3个缓存服务器（节点）：

```
节点0: key % 3 == 0
节点1: key % 3 == 1  
节点2: key % 3 == 2
```

当添加一个新节点时：
- 节点数量从3变为4
- 原来 key % 3 的映射全部失效
- **75%的数据需要重新分布**

这就是**分布式缓存失效**问题。

## 🔍 一致性哈希原理

一致性哈希通过以下方式解决这个问题：

### 1. 哈希环概念

将哈希值空间（0-2³²-1）想象成一个**环**：

```
       2³²-1
         /
        /  
0 ---- 2³¹
```

### 2. 节点映射

每个节点通过哈希计算得到一个在环上的位置：

```go
// 节点位置的计算
hash("node1.example.com") = 123456789
hash("node2.example.com") = 987654321
hash("node3.example.com") = 555555555
```

### 3. 数据定位

对于每个键，找到环上**顺时针方向**的第一个节点：

```
数据A → hash("dataA") = 200000000 → 找到节点2
数据B → hash("dataB") = 800000000 → 找到节点3
```

### 4. 虚拟节点

为了解决数据分布不均的问题，引入**虚拟节点**概念：

```go
// 每个物理节点对应多个虚拟节点
"node1" → "node1-1", "node1-2", "node1-3", ...
"node2" → "node2-1", "node2-2", "node2-3", ...
```

## 💻 代码实现

### 基础实现

查看我们的实现：`pkg/hash/consistent_hash.go`

### 使用示例

```go
package main

import (
    "fmt"
    "github.com/yourusername/distributed-learning-lab/pkg/hash"
)

func main() {
    // 创建一致性哈希环
    ch := hash.NewConsistentHash(10) // 10个虚拟节点
    
    // 添加节点
    ch.Add("node1:8080")
    ch.Add("node2:8080")
    ch.Add("node3:8080")
    
    // 查找数据归属节点
    node := ch.Get("user:123")
    fmt.Printf("用户123的数据应该存储在: %s\n", node)
    
    // 模拟节点故障
    ch.Remove("node2:8080")
    newNode := ch.Get("user:123")
    fmt.Printf("节点2故障后，数据迁移到: %s\n", newNode)
}
```

### 运行示例

```bash
# 运行一致性哈希示例
go run examples/consistent-hash/main.go
```

## 🎯 实践练习

### 练习1: 数据分布验证

编写程序验证10000个键在3个节点上的分布：

```go
func testDistribution() {
    ch := hash.NewConsistentHash(100) // 100个虚拟节点
    nodes := []string{"node1", "node2", "node3"}
    
    for _, node := range nodes {
        ch.Add(node)
    }
    
    distribution := make(map[string]int)
    for i := 0; i < 10000; i++ {
        key := fmt.Sprintf("key-%d", i)
        node := ch.Get(key)
        distribution[node]++
    }
    
    fmt.Printf("数据分布: %v\n", distribution)
}
```

### 练习2: 节点变化影响

测试添加/删除节点时的数据迁移：

```go
func testNodeChange() {
    ch := hash.NewConsistentHash(50)
    
    // 初始3个节点
    initialNodes := []string{"node1", "node2", "node3"}
    for _, node := range initialNodes {
        ch.Add(node)
    }
    
    // 记录初始分布
    initialMapping := make(map[string]string)
    for i := 0; i < 1000; i++ {
        key := fmt.Sprintf("key-%d", i)
        initialMapping[key] = ch.Get(key)
    }
    
    // 添加第4个节点
    ch.Add("node4")
    
    // 计算需要迁移的数据比例
    migrated := 0
    for i := 0; i < 1000; i++ {
        key := fmt.Sprintf("key-%d", i)
        newNode := ch.Get(key)
        if newNode != initialMapping[key] {
            migrated++
        }
    }
    
    fmt.Printf("需要迁移的数据比例: %.2f%%\n", float64(migrated)/10.0)
}
```

## 📊 性能分析

### 虚拟节点数量选择

| 虚拟节点数 | 数据分布均匀性 | 内存消耗 | 查询时间 |
|------------|----------------|----------|----------|
| 10         | 一般           | 低       | 快       |
| 100        | 较好           | 中       | 较快     |
| 1000       | 很好           | 高       | 慢       |

### 实际测试结果

在我们的测试中：
- **100个虚拟节点**：数据分布均匀，性能良好
- **添加节点**：约5-10%数据需要迁移
- **删除节点**：约5-10%数据需要迁移

## 🔧 高级特性

### 加权一致性哈希

可以为不同容量的节点设置权重：

```go
// 节点权重配置
weights := map[string]int{
    "node1": 3,  // 3倍权重
    "node2": 2,  // 2倍权重  
    "node3": 1,  // 标准权重
}

ch := hash.NewWeightedConsistentHash(weights, 100)
```

### 故障转移

结合心跳检测实现自动故障转移：

```go
func (ch *ConsistentHash) HandleNodeFailure(node string) {
    ch.Remove(node)
    go func() {
        for {
            if isNodeAlive(node) {
                ch.Add(node)
                break
            }
            time.Sleep(5 * time.Second)
        }
    }()
}
```

## 🎓 学习总结

通过本教程，你应该：

1. **理解** 一致性哈希的基本原理
2. **掌握** 虚拟节点的使用方法
3. **实现** 基本的哈希环操作
4. **分析** 数据分布和迁移情况
5. **应用** 到实际的分布式系统中

## 🚀 下一步

继续学习：
- [分布式锁实现教程](distributed-lock.md)
- [Raft共识算法教程](raft-consensus.md)
- [Harmoniakv完整系统架构](dynamo-architecture.md)

## 📚 延伸阅读

- [Amazon Dynamo论文](https://www.allthingsdistributed.com/files/amazon-dynamo-sosp2007.pdf)
- [Consistent Hashing and Random Trees](https://dl.acm.org/doi/10.1145/258533.258660)
- [GitHub:一致性哈希可视化工具](https://github.com/stathat/consistent)