package memory

import (
	"sync"

	herror "github.com/suohailong/harmoniakv/internal/util/error"
	"github.com/suohailong/harmoniakv/internal/version"
)

type memoryStore struct {
	sync.Map
}

func (m *memoryStore) Put(key []byte, value version.Value) (e error) {
	m.Store(key, value)
	if _, ok := m.Load(key); !ok {
		return herror.New(herror.ErrPutFailed, "put failed")
	}
	return
}

func (m *memoryStore) Get(key []byte) (_ []*version.Value) {
	panic("not implemented") // TODO: Implement
}
