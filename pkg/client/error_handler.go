// Package client provides client-side error handling utilities
package client

import (
	"fmt"
	"time"

	v1 "github.com/suohailong/harmoniakv/internal/api/v1"
	"github.com/suohailong/harmoniakv/internal/errors"
)

// ErrorHandler provides intelligent error handling for HarmoniaKV clients
type Error<PERSON>andler struct {
	maxRetries          int
	baseDelay           time.Duration
	maxDelay            time.Duration
	backoffMultiplier   float64
	retryableCategories map[string]bool
}

// NewErrorHandler creates a new error handler with default settings
func NewErrorHandler() *ErrorHandler {
	return &ErrorHandler{
		maxRetries:        3,
		baseDelay:         100 * time.Millisecond,
		maxDelay:          10 * time.Second,
		backoffMultiplier: 2.0,
		retryableCategories: map[string]bool{
			"network":     true,
			"quorum":      true,
			"cluster":     true,
			"consistency": false, // Usually needs manual intervention
			"storage":     false, // Usually permanent failures
		},
	}
}

// RetryDecision represents the decision on whether to retry an operation
type RetryDecision struct {
	ShouldRetry bool
	Delay       time.Duration
	Reason      string
	Alternative string // Alternative strategy if available
}

// AnalyzeError analyzes an error and provides a retry decision
func (h *<PERSON>rror<PERSON>and<PERSON>) AnalyzeError(err *v1.Error, attemptCount int) *RetryDecision {
	// Check if we've exceeded max retries
	if attemptCount >= h.maxRetries {
		return &RetryDecision{
			ShouldRetry: false,
			Reason:      fmt.Sprintf("max retries (%d) exceeded", h.maxRetries),
		}
	}

	// Check if error is explicitly marked as non-retryable
	if !err.Retryable {
		return &RetryDecision{
			ShouldRetry: false,
			Reason:      "error marked as non-retryable",
		}
	}

	// Check category-level retryability
	if !h.retryableCategories[err.Category] {
		return &RetryDecision{
			ShouldRetry: false,
			Reason:      fmt.Sprintf("category '%s' not retryable", err.Category),
		}
	}

	// Use server-suggested retry delay if available
	if err.RetryAfterSeconds > 0 {
		delay := time.Duration(err.RetryAfterSeconds) * time.Second
		return &RetryDecision{
			ShouldRetry: true,
			Delay:       delay,
			Reason:      "server-suggested retry delay",
		}
	}

	// Parse error details for intelligent handling
	errorDetails, parseErr := errors.ParseErrorDetails(err.Code, err.Details)
	if parseErr != nil {
		// Fallback to generic retry logic
		return h.genericRetryDecision(attemptCount)
	}

	return h.analyzeSpecificError(errorDetails, attemptCount)
}

// analyzeSpecificError provides error-specific retry logic
func (h *ErrorHandler) analyzeSpecificError(details errors.ErrorDetails, attemptCount int) *RetryDecision {
	switch d := details.(type) {
	case *errors.NetworkUnreachableDetails:
		return h.handleNetworkError(d, attemptCount)
	case *errors.QuorumReadFailedDetails:
		return h.handleQuorumReadError(d, attemptCount)
	case *errors.VersionConflictDetails:
		return h.handleVersionConflictError(d, attemptCount)
	default:
		return h.genericRetryDecision(attemptCount)
	}
}

// handleNetworkError handles network-related errors
func (h *ErrorHandler) handleNetworkError(details *errors.NetworkUnreachableDetails, attemptCount int) *RetryDecision {
	// Calculate exponential backoff
	delay := h.calculateBackoffDelay(attemptCount)

	reason := fmt.Sprintf("network unreachable to %s, will retry with backoff", details.TargetNode)
	alternative := fmt.Sprintf("consider routing to alternative nodes, mark %s as unhealthy", details.TargetNode)

	return &RetryDecision{
		ShouldRetry: true,
		Delay:       delay,
		Reason:      reason,
		Alternative: alternative,
	}
}

// handleQuorumReadError handles quorum read failures
func (h *ErrorHandler) handleQuorumReadError(details *errors.QuorumReadFailedDetails, attemptCount int) *RetryDecision {
	// If we got some successful responses, we might try eventual consistency
	if details.SuccessCount > 0 {
		return &RetryDecision{
			ShouldRetry: true,
			Delay:       50 * time.Millisecond, // Quick retry
			Reason:      fmt.Sprintf("partial success (%d/%d), quick retry", details.SuccessCount, details.RequiredCount),
			Alternative: "consider degrading to eventual consistency read",
		}
	}

	// No successful responses, longer backoff
	delay := h.calculateBackoffDelay(attemptCount)
	return &RetryDecision{
		ShouldRetry: true,
		Delay:       delay,
		Reason:      "complete quorum failure, will retry with backoff",
		Alternative: "check cluster health and node connectivity",
	}
}

// handleVersionConflictError handles version conflicts
func (h *ErrorHandler) handleVersionConflictError(details *errors.VersionConflictDetails, attemptCount int) *RetryDecision {
	if details.AutoResolve {
		// Auto-resolve is enabled, short retry
		return &RetryDecision{
			ShouldRetry: true,
			Delay:       1 * time.Second,
			Reason:      "version conflict with auto-resolve enabled",
		}
	}

	// Manual resolution required
	return &RetryDecision{
		ShouldRetry: false,
		Reason:      "version conflict requires manual resolution",
		Alternative: fmt.Sprintf("use conflict resolver '%s' or implement custom merge logic", details.ConflictResolver),
	}
}

// genericRetryDecision provides fallback retry logic
func (h *ErrorHandler) genericRetryDecision(attemptCount int) *RetryDecision {
	delay := h.calculateBackoffDelay(attemptCount)
	return &RetryDecision{
		ShouldRetry: true,
		Delay:       delay,
		Reason:      "generic retry with exponential backoff",
	}
}

// calculateBackoffDelay calculates exponential backoff delay
func (h *ErrorHandler) calculateBackoffDelay(attemptCount int) time.Duration {
	delay := float64(h.baseDelay)
	for i := 0; i < attemptCount; i++ {
		delay *= h.backoffMultiplier
	}

	finalDelay := time.Duration(delay)
	if finalDelay > h.maxDelay {
		finalDelay = h.maxDelay
	}

	return finalDelay
}

// WithMaxRetries sets the maximum number of retries
func (h *ErrorHandler) WithMaxRetries(maxRetries int) *ErrorHandler {
	h.maxRetries = maxRetries
	return h
}

// WithBaseDelay sets the base delay for exponential backoff
func (h *ErrorHandler) WithBaseDelay(delay time.Duration) *ErrorHandler {
	h.baseDelay = delay
	return h
}

// WithMaxDelay sets the maximum delay for exponential backoff
func (h *ErrorHandler) WithMaxDelay(delay time.Duration) *ErrorHandler {
	h.maxDelay = delay
	return h
}

// Example usage patterns for clients
type ExampleClient struct {
	errorHandler *ErrorHandler
}

// ExampleRead demonstrates intelligent error handling for read operations
func (c *ExampleClient) ExampleRead(key string) ([]byte, error) {
	var lastErr *v1.Error

	for attempt := 0; attempt < c.errorHandler.maxRetries; attempt++ {
		// Simulate read operation
		result, err := c.performRead(key)
		if err == nil {
			return result, nil
		}

		// Analyze error
		decision := c.errorHandler.AnalyzeError(err, attempt)
		lastErr = err

		if !decision.ShouldRetry {
			if decision.Alternative != "" {
				// Log alternative strategy suggestion
				fmt.Printf("Retry failed: %s. Consider: %s\n", decision.Reason, decision.Alternative)
			}
			break
		}

		// Wait before retry
		time.Sleep(decision.Delay)
		fmt.Printf("Retrying in %v: %s\n", decision.Delay, decision.Reason)
	}

	return nil, fmt.Errorf("operation failed after retries: %s", lastErr.Message)
}

// performRead is a placeholder for actual read implementation
func (c *ExampleClient) performRead(key string) ([]byte, error) {
	// This would be replaced with actual gRPC call
	// For demonstration, we return a mock error
	return nil, &v1.Error{
		Code:              2001, // ERROR_CODE_QUORUM_READ_FAILED
		Message:           "quorum read failed",
		Category:          "quorum",
		Retryable:         true,
		RetryAfterSeconds: 0,
		Details: map[string]string{
			"operation_id":   "read-op-12345",
			"key":            key,
			"success_count":  "1",
			"required_count": "2",
			"total_nodes":    "3",
		},
	}
}
