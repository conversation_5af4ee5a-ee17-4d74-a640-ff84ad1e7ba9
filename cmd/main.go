package main

import (
	"context"
	"encoding/json"
	"flag"
	"log"
	"net"

	internal_api "github.com/suohailong/harmoniakv/internal/api/v1"
	"github.com/suohailong/harmoniakv/internal/config"
	"github.com/suohailong/harmoniakv/internal/server"
	v1 "github.com/suohailong/harmoniakv/pkg/api/v1"
	"google.golang.org/grpc"
)

var configPath string

func main() {
	flag.StringVar(&configPath, "config", "config/harmoniakv.yaml", "config file (default is $HOME/.harmoniakv.yaml)")
	flag.Parse()

	// 1. 读取初始配置，通过环境变量或者参数的形式读取
	config.Init(configPath)

	harmonia := server.New()

	// 注册 handler 示例（实际在 server.New 中注册）
	// trans.RegisterHandler(transport.MessageTypeGossip, someHandler)

	// 公共 server
	publicLis, err := net.Listen("tcp", ":50051")
	if err != nil {
		log.Fatalf("public listen failed: %v", err)
	}
	publicGrpc := grpc.NewServer()
	v1.RegisterHarmoniakvServer(publicGrpc, harmonia)
	go publicGrpc.Serve(publicLis)

	// 内部 server
	internalLis, err := net.Listen("tcp", ":50052")
	if err != nil {
		log.Fatalf("internal listen failed: %v", err)
	}
	internalGrpc := grpc.NewServer() // 可加选项如 creds
	internal_api.RegisterInternalHarmoniakvServer(internalGrpc, harmonia)
	internalGrpc.Serve(internalLis)

	// 启动 UDP 监听
	udpAddr, err := net.ResolveUDPAddr("udp", ":50053")
	if err != nil {
		log.Fatalf("udp listen failed: %v", err)
	}
	udpConn, err := net.ListenUDP("udp", udpAddr)
	if err != nil {
		log.Fatalf("udp listen failed: %v", err)
	}
	go handleUDP(udpConn, harmonia) // 启动处理 goroutine
}

// 新增 UDP 处理函数
func handleUDP(conn *net.UDPConn, harmonia server.KvServer) {
	buf := make([]byte, 65535)
	for {
		n, _, err := conn.ReadFromUDP(buf)
		if err != nil {
			log.Printf("UDP read error: %v", err)
			continue
		}
		var req internal_api.InternalRequest
		if err := json.Unmarshal(buf[:n], &req); err != nil {
			log.Printf("UDP unmarshal error: %v", err)
			continue
		}
		ctx := context.Background() // 或使用带超时的 ctx
		harmonia.HandleMessage(ctx, &req)
	}
}
