package server

import (
	"context"
	"encoding/json"

	v1 "github.com/suohailong/harmoniakv/pkg/api/v1"

	"github.com/suohailong/harmoniakv/internal/cluster"
	"github.com/suohailong/harmoniakv/internal/config"
	"github.com/suohailong/harmoniakv/internal/transport"
	"github.com/suohailong/harmoniakv/internal/version"

	// 更新导入路径（从 internal/ 到 pkg/）
	internal_api "github.com/suohailong/harmoniakv/internal/api/v1"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/anypb"
)

type KvServer interface {
	v1.HarmoniakvServer
	internal_api.InternalHarmoniakvServer
	HandleUDPMessage(ctx context.Context, req *internal_api.InternalRequest) error
}

// server 结构体：移除 coordinator，改为持有本地 Node
type server struct {
	v1.UnimplementedHarmoniakvServer
	internal_api.UnimplementedInternalHarmoniakvServer
	localNode *cluster.Node         // 新增：持有本地 Node
	trans     transport.Transporter // 新增 transport 字段
}

// New：创建本地 Node 并注入 Cluster（修复：使用 config.NodeID() 和 config.NodeAddr()）
func New() KvServer {
	trans := transport.NewTransporter() // 初始化 transport
	// 创建 Cluster（使用新包）
	cl := cluster.New(cluster.WithTrans(trans)) // 类型现在是 node.Cluster，但由于注入到 NewNode，无需更改
	// 创建本地 Node
	localNode := cluster.NewNode(config.NodeID(), config.NodeAddr(), cl, trans)
	cl.AddNode(localNode)
	for _, peer := range config.Peers() {
		cl.AddNode(cluster.NewNode(peer, peer, cl, trans))
	}

	go cl.Start()

	// 注册 handler 示例
	trans.RegisterHandler(internal_api.MessageType_MessageTypeGossip, func(ctx context.Context, req *internal_api.InternalRequest) error {
		var gsm cluster.GossipStateMessage
		// 从 protobuf Any 中提取数据
		if req.Data != nil && req.Data.TypeUrl == "application/json" {
			if err := json.Unmarshal(req.Data.Value, &gsm); err != nil {
				return err
			}
		}
		cl.HandleGossipMessage(&gsm)
		return nil
	})
	trans.RegisterHandler(internal_api.MessageType_MessageTypeKvCommand, func(ctx context.Context, req *internal_api.InternalRequest) error {
		var cmd cluster.KvCommand
		// 从 protobuf Any 中提取数据
		if req.Data != nil && req.Data.TypeUrl == "application/json" {
			var data map[string]interface{}
			if err := json.Unmarshal(req.Data.Value, &data); err != nil {
				return err
			}
			if cmdVal, ok := data["Command"].(float64); ok {
				cmd.Command = uint32(cmdVal)
			}
			if keyVal, ok := data["Key"].(string); ok {
				cmd.Key = []byte(keyVal)
			}
			// Value 类似处理
		}
		_, err := localNode.HandleCommand(&cmd)
		return err
	})
	trans.RegisterHandler(internal_api.MessageType_MessageTypeNodeJoin, func(ctx context.Context, req *internal_api.InternalRequest) error {
		// 类似转换和调用 cl.HandleNodeJoin
		return cl.HandleNodeJoin(ctx, req)
	})
	// 类似为其他类型添加包装
	trans.RegisterHandler(internal_api.MessageType_MessageTypeNodeLeave, func(ctx context.Context, req *internal_api.InternalRequest) error {
		return cl.HandleNodeLeave(ctx, req)
	})
	trans.RegisterHandler(internal_api.MessageType_MessageTypeAntiEntropy, func(ctx context.Context, req *internal_api.InternalRequest) error {
		return cl.HandleAntiEntropy(ctx, req)
	})
	trans.RegisterHandler(internal_api.MessageType_MessageTypeHintedHandoff, func(ctx context.Context, req *internal_api.InternalRequest) error {
		return cl.HandleHintedHandoff(ctx, req)
	})
	trans.RegisterHandler(internal_api.MessageType_MessageTypeQuorumAck, func(ctx context.Context, req *internal_api.InternalRequest) error {
		return cl.HandleQuorumAck(ctx, req)
	})
	trans.RegisterHandler(internal_api.MessageType_MessageTypeVersionMerge, func(ctx context.Context, req *internal_api.InternalRequest) error {
		return cl.HandleVersionMerge(ctx, req)
	})
	trans.RegisterHandler(internal_api.MessageType_MessageTypeClusterQuery, func(ctx context.Context, req *internal_api.InternalRequest) error {
		return cl.HandleClusterQuery(ctx, req)
	})

	return &server{
		localNode: localNode,
		trans:     trans,
	}
}

// Get：调用本地 Node 的协调方法（修复：传递 ctx）
func (s *server) Get(ctx context.Context, req *v1.GetRequest) (*v1.GetResponse, error) {
	// TODO: 如何处理ctx 之后再议（迁移时传递 ctx）
	objects, err := s.localNode.CoordinateGet(ctx, req.Key)
	if err != nil {
		return nil, err
	}
	return &v1.GetResponse{Objects: objects}, nil
}

// Put：调用本地 Node 的协调方法（修复：传递 ctx）
func (s *server) Put(ctx context.Context, req *v1.PutRequest) (*v1.PutResponse, error) {
	value := &version.Value{
		Key:           req.Key,
		Value:         req.Value,
		VersionVector: &version.Vector{},
	}

	err := s.localNode.CoordinatePut(ctx, req.Key, value)
	return &v1.PutResponse{}, err
}

// 在 server 结构体添加
func (s *server) HandleMessage(ctx context.Context, req *internal_api.MessageRequest) (*internal_api.MessageResponse, error) {
	if req.Token != config.InternalToken() { // 示例认证
		return nil, status.Error(codes.Unauthenticated, "invalid token")
	}

	// 直接传递请求到传输层，无需转换
	err := s.trans.Receive(ctx, req)
	if err != nil {
		return nil, err
	}

	// 返回响应，使用 Any 类型保持一致性
	respData := &anypb.Any{
		TypeUrl: "application/json",
		Value:   []byte("{}"), // 空 JSON 对象
	}
	return &internal_api.SendMessageResponse{Data: respData}, nil
}
